extends Node

## Biome system managing environmental themes, lighting, and atmosphere
## Implements the Strategy pattern for different biome behaviors

signal biome_changed(old_biome: GameEnums.BiomeType, new_biome: GameEnums.BiomeType)
signal environmental_effect_applied(effect_name: String, intensity: float)
signal weather_changed(weather_type: String)

@export var transition_duration: float = 2.0
@export var dynamic_lighting: bool = true
@export var weather_effects: bool = true

# Current biome state
var current_biome: GameEnums.BiomeType = GameEnums.BiomeType.FOREST
var biome_transition_progress: float = 0.0
var is_transitioning: bool = false

# Biome configurations
var biome_configs: Dictionary = {}
var weather_systems: Dictionary = {}

# Environment references
var directional_light: DirectionalLight3D
var environment: Environment
var world_environment: WorldEnvironment
var fog_volume: FogVolume

# Transition tween
var transition_tween: Tween

func _ready() -> void:
	_setup_biome_configurations()
	_setup_weather_systems()
	_find_environment_nodes()

func initialize(scene: Node3D) -> void:
	_find_environment_nodes_in_scene(scene)
	_apply_biome_immediately(current_biome)

func _find_environment_nodes() -> void:
	# Find environment nodes in the scene
	var scene = get_tree().current_scene
	_find_environment_nodes_in_scene(scene)

func _find_environment_nodes_in_scene(scene: Node) -> void:
	# Find lighting and environment nodes
	var lights = scene.find_children("*", "DirectionalLight3D")
	directional_light = lights[0] if lights.size() > 0 else null
	
	var world_envs = scene.find_children("*", "WorldEnvironment")
	world_environment = world_envs[0] if world_envs.size() > 0 else null
	
	if world_environment:
		environment = world_environment.environment

func _setup_biome_configurations() -> void:
	# Define biome visual and gameplay characteristics
	biome_configs = {
		GameEnums.BiomeType.FOREST: {
			"name": "Enchanted Forest",
			"lighting": {
				"color": Color(1.0, 0.95, 0.8),
				"energy": 1.0,
				"shadow_enabled": true
			},
			"fog": {
				"enabled": true,
				"color": Color(0.7, 0.8, 0.7, 0.3),
				"density": 0.02,
				"height": 10.0
			},
			"ambient": {
				"sky_color": Color(0.4, 0.6, 1.0),
				"ground_color": Color(0.2, 0.4, 0.2),
				"energy": 0.3
			},
			"particle_effects": ["falling_leaves", "fireflies"],
			"audio_ambience": "ambient_forest",
			"enemy_modifiers": {
				"spawn_rate": 1.0,
				"health_multiplier": 1.0,
				"speed_multiplier": 1.0
			},
			"special_features": ["healing_springs", "hidden_paths"]
		},
		
		GameEnums.BiomeType.DESERT: {
			"name": "Scorching Desert",
			"lighting": {
				"color": Color(1.0, 0.9, 0.6),
				"energy": 1.3,
				"shadow_enabled": true
			},
			"fog": {
				"enabled": true,
				"color": Color(0.9, 0.8, 0.6, 0.2),
				"density": 0.01,
				"height": 5.0
			},
			"ambient": {
				"sky_color": Color(1.0, 0.8, 0.4),
				"ground_color": Color(0.8, 0.6, 0.3),
				"energy": 0.4
			},
			"particle_effects": ["sand_storm", "heat_shimmer"],
			"audio_ambience": "wind",
			"enemy_modifiers": {
				"spawn_rate": 1.2,
				"health_multiplier": 0.9,
				"speed_multiplier": 1.1
			},
			"special_features": ["mirages", "sandstorms"],
			"environmental_damage": {
				"type": "heat",
				"damage_per_second": 1,
				"immunity_items": ["desert_cloak"]
			}
		},
		
		GameEnums.BiomeType.ICE: {
			"name": "Frozen Wasteland",
			"lighting": {
				"color": Color(0.8, 0.9, 1.0),
				"energy": 0.8,
				"shadow_enabled": true
			},
			"fog": {
				"enabled": true,
				"color": Color(0.9, 0.95, 1.0, 0.4),
				"density": 0.03,
				"height": 8.0
			},
			"ambient": {
				"sky_color": Color(0.6, 0.8, 1.0),
				"ground_color": Color(0.8, 0.9, 1.0),
				"energy": 0.2
			},
			"particle_effects": ["snow_fall", "ice_crystals"],
			"audio_ambience": "wind",
			"enemy_modifiers": {
				"spawn_rate": 0.8,
				"health_multiplier": 1.2,
				"speed_multiplier": 0.8
			},
			"special_features": ["ice_platforms", "frozen_lakes"],
			"environmental_damage": {
				"type": "cold",
				"damage_per_second": 1,
				"immunity_items": ["winter_coat"]
			}
		},
		
		GameEnums.BiomeType.VOLCANO: {
			"name": "Volcanic Crater",
			"lighting": {
				"color": Color(1.0, 0.6, 0.3),
				"energy": 1.2,
				"shadow_enabled": true
			},
			"fog": {
				"enabled": true,
				"color": Color(0.8, 0.4, 0.2, 0.5),
				"density": 0.04,
				"height": 15.0
			},
			"ambient": {
				"sky_color": Color(1.0, 0.4, 0.2),
				"ground_color": Color(0.6, 0.2, 0.1),
				"energy": 0.5
			},
			"particle_effects": ["lava_sparks", "ash_fall"],
			"audio_ambience": "fire",
			"enemy_modifiers": {
				"spawn_rate": 1.3,
				"health_multiplier": 1.1,
				"speed_multiplier": 1.2
			},
			"special_features": ["lava_flows", "geysers"],
			"environmental_damage": {
				"type": "fire",
				"damage_per_second": 2,
				"immunity_items": ["fire_resistance_potion"]
			}
		},
		
		GameEnums.BiomeType.SHADOW: {
			"name": "Shadow Realm",
			"lighting": {
				"color": Color(0.5, 0.3, 0.8),
				"energy": 0.6,
				"shadow_enabled": true
			},
			"fog": {
				"enabled": true,
				"color": Color(0.3, 0.2, 0.5, 0.6),
				"density": 0.05,
				"height": 20.0
			},
			"ambient": {
				"sky_color": Color(0.2, 0.1, 0.4),
				"ground_color": Color(0.1, 0.1, 0.2),
				"energy": 0.1
			},
			"particle_effects": ["shadow_wisps", "dark_energy"],
			"audio_ambience": "dark_whispers",
			"enemy_modifiers": {
				"spawn_rate": 1.5,
				"health_multiplier": 1.3,
				"speed_multiplier": 1.3
			},
			"special_features": ["shadow_portals", "void_zones"],
			"environmental_damage": {
				"type": "shadow",
				"damage_per_second": 3,
				"immunity_items": ["light_amulet"]
			}
		},
		
		GameEnums.BiomeType.CELESTIAL: {
			"name": "Celestial Gardens",
			"lighting": {
				"color": Color(1.0, 1.0, 0.9),
				"energy": 1.5,
				"shadow_enabled": false
			},
			"fog": {
				"enabled": false,
				"color": Color.WHITE,
				"density": 0.0,
				"height": 0.0
			},
			"ambient": {
				"sky_color": Color(0.9, 0.95, 1.0),
				"ground_color": Color(1.0, 1.0, 0.9),
				"energy": 0.6
			},
			"particle_effects": ["golden_sparkles", "light_rays"],
			"audio_ambience": "celestial_choir",
			"enemy_modifiers": {
				"spawn_rate": 0.7,
				"health_multiplier": 1.5,
				"speed_multiplier": 1.0
			},
			"special_features": ["healing_aura", "divine_blessing"],
			"environmental_healing": {
				"heal_per_second": 2,
				"mana_regen_bonus": 1.5
			}
		}
	}

func _setup_weather_systems() -> void:
	# Define weather patterns for each biome
	weather_systems = {
		GameEnums.BiomeType.FOREST: ["clear", "light_rain", "fog"],
		GameEnums.BiomeType.DESERT: ["clear", "sandstorm", "heat_wave"],
		GameEnums.BiomeType.ICE: ["clear", "blizzard", "aurora"],
		GameEnums.BiomeType.VOLCANO: ["clear", "ash_storm", "lava_rain"],
		GameEnums.BiomeType.SHADOW: ["clear", "shadow_storm", "void_eclipse"],
		GameEnums.BiomeType.CELESTIAL: ["clear", "light_shower", "divine_radiance"]
	}

## Biome Transition System
func change_biome(new_biome: GameEnums.BiomeType, immediate: bool = false) -> void:
	if new_biome == current_biome:
		return
	
	var old_biome = current_biome
	
	if immediate:
		_apply_biome_immediately(new_biome)
	else:
		_start_biome_transition(new_biome)
	
	biome_changed.emit(old_biome, new_biome)
	
	# Update audio (placeholder - AudioManager should be autoload)
	# if AudioManager:
	#	AudioManager.set_biome_audio(new_biome)

func _apply_biome_immediately(biome: GameEnums.BiomeType) -> void:
	current_biome = biome
	var config = biome_configs[biome]
	
	_apply_lighting_config(config.lighting)
	_apply_fog_config(config.fog)
	_apply_ambient_config(config.ambient)
	_apply_particle_effects(config.particle_effects)
	_apply_environmental_effects(config)

func _start_biome_transition(new_biome: GameEnums.BiomeType) -> void:
	if is_transitioning:
		if transition_tween:
			transition_tween.kill()
	
	is_transitioning = true
	var old_biome = current_biome
	current_biome = new_biome
	
	transition_tween = create_tween()
	transition_tween.tween_method(
		_update_biome_transition.bind(old_biome, new_biome),
		0.0,
		1.0,
		transition_duration
	)
	transition_tween.tween_callback(_finish_biome_transition)

func _update_biome_transition(old_biome: GameEnums.BiomeType, new_biome: GameEnums.BiomeType, progress: float) -> void:
	biome_transition_progress = progress
	
	var old_config = biome_configs[old_biome]
	var new_config = biome_configs[new_biome]
	
	# Interpolate lighting
	_interpolate_lighting(old_config.lighting, new_config.lighting, progress)
	
	# Interpolate fog
	_interpolate_fog(old_config.fog, new_config.fog, progress)
	
	# Interpolate ambient
	_interpolate_ambient(old_config.ambient, new_config.ambient, progress)

func _finish_biome_transition() -> void:
	is_transitioning = false
	biome_transition_progress = 1.0
	
	var config = biome_configs[current_biome]
	_apply_particle_effects(config.particle_effects)
	_apply_environmental_effects(config)

## Environment Configuration
func _apply_lighting_config(lighting_config: Dictionary) -> void:
	if not directional_light:
		return
	
	directional_light.light_color = lighting_config.color
	directional_light.light_energy = lighting_config.energy
	directional_light.shadow_enabled = lighting_config.shadow_enabled

func _apply_fog_config(fog_config: Dictionary) -> void:
	if not environment:
		return
	
	environment.fog_enabled = fog_config.enabled
	if fog_config.enabled:
		environment.fog_light_color = fog_config.color
		environment.fog_density = fog_config.density
		environment.fog_height = fog_config.height

func _apply_ambient_config(ambient_config: Dictionary) -> void:
	if not environment:
		return
	
	environment.ambient_light_color = ambient_config.sky_color
	environment.ambient_light_energy = ambient_config.energy

func _interpolate_lighting(old_config: Dictionary, new_config: Dictionary, progress: float) -> void:
	if not directional_light:
		return
	
	directional_light.light_color = old_config.color.lerp(new_config.color, progress)
	directional_light.light_energy = lerp(old_config.energy, new_config.energy, progress)

func _interpolate_fog(old_config: Dictionary, new_config: Dictionary, progress: float) -> void:
	if not environment:
		return
	
	if old_config.enabled or new_config.enabled:
		environment.fog_enabled = true
		environment.fog_light_color = old_config.color.lerp(new_config.color, progress)
		environment.fog_density = lerp(old_config.density, new_config.density, progress)
		environment.fog_height = lerp(old_config.height, new_config.height, progress)

func _interpolate_ambient(old_config: Dictionary, new_config: Dictionary, progress: float) -> void:
	if not environment:
		return
	
	environment.ambient_light_color = old_config.sky_color.lerp(new_config.sky_color, progress)
	environment.ambient_light_energy = lerp(old_config.energy, new_config.energy, progress)

## Environmental Effects
func _apply_particle_effects(effects: Array) -> void:
	# Clear existing particle effects
	_clear_particle_effects()
	
	# Apply new particle effects
	for effect_name in effects:
		EventBus.effect_requested.emit(effect_name, Vector3.ZERO)

func _clear_particle_effects() -> void:
	# Remove existing environmental particle effects
	EventBus.emit_signal("clear_environmental_effects")

func _apply_environmental_effects(config: Dictionary) -> void:
	# Apply environmental damage/healing
	if config.has("environmental_damage"):
		var damage_config = config.environmental_damage
		EventBus.emit_signal("environmental_damage_applied", damage_config)
	
	if config.has("environmental_healing"):
		var healing_config = config.environmental_healing
		EventBus.emit_signal("environmental_healing_applied", healing_config)
	
	# Apply special features
	if config.has("special_features"):
		for feature in config.special_features:
			EventBus.emit_signal("special_feature_activated", feature)

## Weather System
func trigger_weather_event(weather_type: String) -> void:
	var current_weather_types = weather_systems.get(current_biome, ["clear"])
	
	if weather_type not in current_weather_types:
		print("Weather type '%s' not available in biome '%s'" % [weather_type, GameEnums.BiomeType.keys()[current_biome]])
		return
	
	_apply_weather_effects(weather_type)
	weather_changed.emit(weather_type)

func _apply_weather_effects(weather_type: String) -> void:
	match weather_type:
		"clear":
			_clear_weather_effects()
		"light_rain":
			_apply_rain_effects(0.3)
		"blizzard":
			_apply_snow_effects(0.8)
		"sandstorm":
			_apply_sand_effects(0.6)
		"ash_storm":
			_apply_ash_effects(0.7)
		"shadow_storm":
			_apply_shadow_effects(0.9)
		"light_shower":
			_apply_light_effects(0.5)

func _clear_weather_effects() -> void:
	EventBus.emit_signal("weather_effects_cleared")

func _apply_rain_effects(intensity: float) -> void:
	EventBus.effect_requested.emit("rain", Vector3.ZERO)
	environmental_effect_applied.emit("rain", intensity)

func _apply_snow_effects(intensity: float) -> void:
	EventBus.effect_requested.emit("snow_fall", Vector3.ZERO)
	environmental_effect_applied.emit("snow", intensity)

func _apply_sand_effects(intensity: float) -> void:
	EventBus.effect_requested.emit("sand_storm", Vector3.ZERO)
	environmental_effect_applied.emit("sandstorm", intensity)

func _apply_ash_effects(intensity: float) -> void:
	EventBus.effect_requested.emit("ash_fall", Vector3.ZERO)
	environmental_effect_applied.emit("ash", intensity)

func _apply_shadow_effects(intensity: float) -> void:
	EventBus.effect_requested.emit("shadow_storm", Vector3.ZERO)
	environmental_effect_applied.emit("shadow", intensity)

func _apply_light_effects(intensity: float) -> void:
	EventBus.effect_requested.emit("light_shower", Vector3.ZERO)
	environmental_effect_applied.emit("light", intensity)

## Utility Functions
func get_current_biome() -> GameEnums.BiomeType:
	return current_biome

func get_biome_name() -> String:
	return biome_configs[current_biome].name

func get_biome_config() -> Dictionary:
	return biome_configs[current_biome]

func get_enemy_modifiers() -> Dictionary:
	return biome_configs[current_biome].get("enemy_modifiers", {})

func is_biome_unlocked(biome: GameEnums.BiomeType) -> bool:
	var unlock_level = GameConstants.get_biome_unlock_level(biome)
	var player_level = 1  # Get from player stats
	return player_level >= unlock_level

func get_available_weather_types() -> Array:
	return weather_systems.get(current_biome, ["clear"])

## Debug Functions
func debug_cycle_biomes() -> void:
	var biomes = GameEnums.BiomeType.values()
	var current_index = biomes.find(current_biome)
	var next_index = (current_index + 1) % biomes.size()
	change_biome(biomes[next_index])

func debug_trigger_random_weather() -> void:
	var available_weather = get_available_weather_types()
	var random_weather = available_weather[randi() % available_weather.size()]
	trigger_weather_event(random_weather)

func debug_print_biome_state() -> void:
	print("=== Biome Manager Debug ===")
	print("Current Biome: %s" % get_biome_name())
	print("Transitioning: %s" % is_transitioning)
	if is_transitioning:
		print("Transition Progress: %.2f" % biome_transition_progress)
	print("Available Weather: %s" % get_available_weather_types())
	print("============================")
