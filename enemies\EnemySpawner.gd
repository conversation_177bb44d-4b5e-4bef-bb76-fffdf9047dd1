extends Node

## Enemy spawning system implementing the Factory pattern
## Manages wave-based enemy generation and difficulty scaling

signal wave_enemies_spawned(enemy_count: int)
signal all_enemies_defeated()
signal elite_enemy_spawned(enemy: Enemy)
signal boss_enemy_spawned(enemy: Enemy)

@export var spawn_radius_min: float = 8.0
@export var spawn_radius_max: float = 15.0
@export var spawn_height: float = 1.0
@export var max_enemies_alive: int = 20

# Enemy templates
var enemy_templates: Dictionary = {}
var active_enemies: Array[Enemy] = []
var spawn_points: Array[Vector3] = []

# Wave management
var current_wave: int = 1
var enemies_to_spawn: int = 0
var enemies_spawned_this_wave: int = 0
var spawn_timer: float = 0.0
var spawn_interval: float = 1.0

# References
var main_scene: Node3D
var player: Node3D

func _ready() -> void:
	_load_enemy_templates()
	_connect_events()

func initialize(scene: Node3D) -> void:
	main_scene = scene
	player = scene.get_player() if scene.has_method("get_player") else null
	_generate_spawn_points()

func _load_enemy_templates() -> void:
	# Define enemy templates with stats and behaviors
	enemy_templates = {
		"grunt": {
			"scene_path": "res://enemies/Grunt.tscn",
			"enemy_type": GameEnums.EnemyType.GRUNT,
			"behavior": GameEnums.EnemyBehavior.AGGRESSIVE,
			"base_health": 30,
			"base_damage": 8,
			"move_speed": 3.0,
			"spawn_weight": 60
		},
		"archer": {
			"scene_path": "res://enemies/Archer.tscn",
			"enemy_type": GameEnums.EnemyType.GRUNT,
			"behavior": GameEnums.EnemyBehavior.RANGED,
			"base_health": 20,
			"base_damage": 12,
			"move_speed": 2.5,
			"attack_range": 6.0,
			"spawn_weight": 25
		},
		"tank": {
			"scene_path": "res://enemies/Tank.tscn",
			"enemy_type": GameEnums.EnemyType.GRUNT,
			"behavior": GameEnums.EnemyBehavior.DEFENSIVE,
			"base_health": 60,
			"base_damage": 6,
			"move_speed": 2.0,
			"spawn_weight": 15
		},
		"elite_warrior": {
			"scene_path": "res://enemies/EliteWarrior.tscn",
			"enemy_type": GameEnums.EnemyType.ELITE,
			"behavior": GameEnums.EnemyBehavior.AGGRESSIVE,
			"base_health": 100,
			"base_damage": 20,
			"move_speed": 3.5,
			"spawn_weight": 8
		},
		"boss_ogre": {
			"scene_path": "res://enemies/BossOgre.tscn",
			"enemy_type": GameEnums.EnemyType.BOSS,
			"behavior": GameEnums.EnemyBehavior.BERSERKER,
			"base_health": 300,
			"base_damage": 40,
			"move_speed": 2.5,
			"spawn_weight": 1
		}
	}

func _connect_events() -> void:
	EventBus.wave_started.connect(_on_wave_started)
	EventBus.enemy_killed.connect(_on_enemy_killed)

func _generate_spawn_points() -> void:
	# Generate spawn points in a circle around the arena
	spawn_points.clear()
	var point_count = 12
	
	for i in point_count:
		var angle = (i * TAU) / point_count
		var distance = randf_range(spawn_radius_min, spawn_radius_max)
		var spawn_point = Vector3(
			cos(angle) * distance,
			spawn_height,
			sin(angle) * distance
		)
		spawn_points.append(spawn_point)

func _process(delta: float) -> void:
	_update_spawning(delta)
	_cleanup_dead_enemies()

## Wave Management
func _on_wave_started(wave_number: int) -> void:
	current_wave = wave_number
	_start_wave_spawning()

func _start_wave_spawning() -> void:
	enemies_to_spawn = _calculate_enemies_for_wave(current_wave)
	enemies_spawned_this_wave = 0
	spawn_timer = 0.0
	
	# Adjust spawn interval based on wave
	spawn_interval = max(0.5, 2.0 - (current_wave * 0.1))
	
	print("Starting wave %d: %d enemies to spawn" % [current_wave, enemies_to_spawn])

func _calculate_enemies_for_wave(wave: int) -> int:
	var base_count = GameConstants.ENEMIES_PER_WAVE_BASE
	var scaled_count = base_count * pow(GameConstants.WAVE_SCALING_FACTOR, wave - 1)
	return min(int(scaled_count), GameConstants.MAX_ENEMIES_PER_WAVE)

## Spawning System
func _update_spawning(delta: float) -> void:
	if enemies_to_spawn <= 0:
		return
	
	spawn_timer += delta
	
	if spawn_timer >= spawn_interval and active_enemies.size() < max_enemies_alive:
		_spawn_next_enemy()
		spawn_timer = 0.0

func _spawn_next_enemy() -> void:
	if enemies_spawned_this_wave >= enemies_to_spawn:
		return
	
	var enemy_template = _select_enemy_template()
	var spawn_position = _get_spawn_position()
	var enemy = _create_enemy(enemy_template, spawn_position)
	
	if enemy:
		enemies_spawned_this_wave += 1
		enemies_to_spawn -= 1
		
		if enemies_spawned_this_wave >= _calculate_enemies_for_wave(current_wave):
			wave_enemies_spawned.emit(enemies_spawned_this_wave)

func _select_enemy_template() -> Dictionary:
	# Select enemy type based on wave and weights
	var available_templates = _get_available_templates_for_wave()
	var total_weight = 0
	
	for template in available_templates.values():
		total_weight += template.spawn_weight
	
	var roll = randi() % total_weight
	var cumulative_weight = 0
	
	for template_name in available_templates:
		var template = available_templates[template_name]
		cumulative_weight += template.spawn_weight
		if roll < cumulative_weight:
			return template
	
	# Fallback to grunt
	return enemy_templates["grunt"]

func _get_available_templates_for_wave() -> Dictionary:
	var available = {}
	
	# Always include basic enemies
	available["grunt"] = enemy_templates["grunt"]
	available["archer"] = enemy_templates["archer"]
	
	# Add tank from wave 3
	if current_wave >= 3:
		available["tank"] = enemy_templates["tank"]
	
	# Add elites from wave 5
	if current_wave >= 5:
		available["elite_warrior"] = enemy_templates["elite_warrior"]
	
	# Boss waves (every 5th wave)
	if current_wave % 5 == 0:
		available.clear()  # Only boss on boss waves
		available["boss_ogre"] = enemy_templates["boss_ogre"]
	
	return available

func _get_spawn_position() -> Vector3:
	# Try to find a spawn point away from player
	var best_position = spawn_points[0]
	var max_distance = 0.0
	
	if player:
		for spawn_point in spawn_points:
			var distance = spawn_point.distance_to(player.global_position)
			if distance > max_distance:
				max_distance = distance
				best_position = spawn_point
	else:
		# Random spawn point if no player reference
		best_position = spawn_points[randi() % spawn_points.size()]
	
	# Add some randomization
	var random_offset = Vector3(
		randf_range(-2.0, 2.0),
		0,
		randf_range(-2.0, 2.0)
	)
	
	return best_position + random_offset

## Enemy Creation
func _create_enemy(template: Dictionary, position: Vector3) -> Enemy:
	# Create enemy instance
	var enemy = _instantiate_enemy_from_template(template)
	if not enemy:
		return null
	
	# Set position and level
	enemy.global_position = position
	enemy.set_level(current_wave)
	
	# Apply template properties
	_apply_template_to_enemy(enemy, template)
	
	# Add to scene and tracking
	if main_scene:
		main_scene.add_child(enemy)
	else:
		get_tree().current_scene.add_child(enemy)
	
	active_enemies.append(enemy)
	
	# Connect enemy signals
	enemy.enemy_died.connect(_on_enemy_died)
	
	# Emit appropriate signals
	match enemy.enemy_type:
		GameEnums.EnemyType.ELITE:
			elite_enemy_spawned.emit(enemy)
		GameEnums.EnemyType.BOSS:
			boss_enemy_spawned.emit(enemy)
	
	print("Spawned %s at %s" % [template.get("scene_path", "unknown"), position])
	return enemy

func _instantiate_enemy_from_template(template: Dictionary) -> Enemy:
	# For now, create a basic enemy since we don't have scene files yet
	# In a full implementation, you would load the actual scene files
	var enemy = preload("res://enemies/Enemy.gd").new()
	
	# Add basic visual representation
	var mesh_instance = MeshInstance3D.new()
	var mesh = CapsuleMesh.new()
	mesh.radius = 0.5
	mesh.height = 2.0
	mesh_instance.mesh = mesh
	enemy.add_child(mesh_instance)
	
	# Add collision
	var collision = CollisionShape3D.new()
	var shape = CapsuleShape3D.new()
	shape.radius = 0.5
	shape.height = 2.0
	collision.shape = shape
	enemy.add_child(collision)
	
	# Add navigation agent
	var nav_agent = NavigationAgent3D.new()
	enemy.add_child(nav_agent)
	
	# Add detection areas
	var detection_area = Area3D.new()
	var detection_collision = CollisionShape3D.new()
	var detection_shape = SphereShape3D.new()
	detection_shape.radius = 8.0
	detection_collision.shape = detection_shape
	detection_area.add_child(detection_collision)
	enemy.add_child(detection_area)
	
	var attack_area = Area3D.new()
	var attack_collision = CollisionShape3D.new()
	var attack_shape = SphereShape3D.new()
	attack_shape.radius = 2.0
	attack_collision.shape = attack_shape
	attack_area.add_child(attack_collision)
	enemy.add_child(attack_area)
	
	return enemy

func _apply_template_to_enemy(enemy: Enemy, template: Dictionary) -> void:
	# Apply template properties to enemy
	enemy.enemy_type = template.get("enemy_type", GameEnums.EnemyType.GRUNT)
	enemy.behavior_type = template.get("behavior", GameEnums.EnemyBehavior.AGGRESSIVE)
	enemy.move_speed = template.get("move_speed", 3.0)
	enemy.attack_range = template.get("attack_range", 2.0)
	
	# Scale base stats by level
	var level_multiplier = 1.0 + (enemy.level - 1) * 0.2
	enemy.max_health = int(template.get("base_health", 30) * level_multiplier)
	enemy.current_health = enemy.max_health
	enemy.attack_damage = int(template.get("base_damage", 8) * level_multiplier)
	
	# Color coding by type
	var mesh_instance = enemy.get_node("MeshInstance3D")
	if mesh_instance:
		var material = StandardMaterial3D.new()
		match enemy.enemy_type:
			GameEnums.EnemyType.GRUNT:
				material.albedo_color = Color.RED
			GameEnums.EnemyType.ELITE:
				material.albedo_color = Color.PURPLE
			GameEnums.EnemyType.BOSS:
				material.albedo_color = Color.DARK_RED
		mesh_instance.material_override = material

## Enemy Management
func _on_enemy_died(enemy: Enemy, experience: int, loot_data: Dictionary) -> void:
	# Remove from active list
	var index = active_enemies.find(enemy)
	if index != -1:
		active_enemies.remove_at(index)
	
	# Check if wave is complete
	if active_enemies.is_empty() and enemies_to_spawn <= 0:
		all_enemies_defeated.emit()
		EventBus.wave_completed.emit(current_wave)

func _on_enemy_killed(enemy: Node, experience: int) -> void:
	# Handle enemy death rewards
	if player and player.has_method("add_experience"):
		player.add_experience(experience)

func _cleanup_dead_enemies() -> void:
	# Remove invalid enemy references
	for i in range(active_enemies.size() - 1, -1, -1):
		if not is_instance_valid(active_enemies[i]):
			active_enemies.remove_at(i)

## Special Spawning
func spawn_elite_enemy(position: Vector3 = Vector3.ZERO) -> Enemy:
	if position == Vector3.ZERO:
		position = _get_spawn_position()
	
	var template = enemy_templates["elite_warrior"]
	return _create_enemy(template, position)

func spawn_boss_enemy(position: Vector3 = Vector3.ZERO) -> Enemy:
	if position == Vector3.ZERO:
		position = _get_spawn_position()
	
	var template = enemy_templates["boss_ogre"]
	return _create_enemy(template, position)

func force_spawn_enemy(template_name: String, position: Vector3 = Vector3.ZERO) -> Enemy:
	if not enemy_templates.has(template_name):
		print("Unknown enemy template: %s" % template_name)
		return null
	
	if position == Vector3.ZERO:
		position = _get_spawn_position()
	
	var template = enemy_templates[template_name]
	return _create_enemy(template, position)

## Utility Functions
func get_active_enemy_count() -> int:
	return active_enemies.size()

func get_enemies_remaining_to_spawn() -> int:
	return enemies_to_spawn

func clear_all_enemies() -> void:
	for enemy in active_enemies:
		if is_instance_valid(enemy):
			enemy.queue_free()
	active_enemies.clear()
	enemies_to_spawn = 0

func pause_spawning() -> void:
	enemies_to_spawn = 0

func resume_spawning(remaining_enemies: int) -> void:
	enemies_to_spawn = remaining_enemies

## Debug Functions
func debug_spawn_test_enemies() -> void:
	for template_name in enemy_templates:
		var position = _get_spawn_position()
		force_spawn_enemy(template_name, position)

func debug_print_spawner_state() -> void:
	print("=== Enemy Spawner Debug ===")
	print("Current Wave: %d" % current_wave)
	print("Active Enemies: %d" % active_enemies.size())
	print("Enemies to Spawn: %d" % enemies_to_spawn)
	print("Enemies Spawned This Wave: %d" % enemies_spawned_this_wave)
	print("============================")
