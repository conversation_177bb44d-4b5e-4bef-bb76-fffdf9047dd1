class_name Consumable
extends Item

## Consumable class implementing potions, scrolls, and other usable items
## Provides immediate effects and temporary buffs/debuffs

@export var consumable_type: String = "potion"  # potion, scroll, food, elixir
@export var heal_amount: int = 0
@export var mana_amount: int = 0
@export var buff_duration: float = 0.0
@export var buff_effects: Dictionary = {}

# Consumable-specific properties
@export var instant_effects: Dictionary = {}
@export var over_time_effects: Dictionary = {}
@export var use_animation: String = ""
@export var use_sound: String = ""
@export var particle_effect: String = ""

# Usage restrictions
@export var combat_usable: bool = true
@export var cooldown_time: float = 0.0
@export var requires_target: bool = false

# State tracking
var last_use_time: float = 0.0
var is_on_cooldown: bool = false

func _init() -> void:
	super._init()
	item_type = GameEnums.ItemType.CONSUMABLE
	is_consumable = true

func _setup_base_properties() -> void:
	super._setup_base_properties()
	_setup_consumable_defaults()

func _setup_consumable_defaults() -> void:
	# Set default properties based on consumable type
	match consumable_type:
		"potion":
			stack_size = 10
			use_animation = "drink"
			use_sound = "potion_drink"
		"scroll":
			stack_size = 5
			use_animation = "cast"
			use_sound = "scroll_use"
			particle_effect = "magic_sparkles"
		"food":
			stack_size = 20
			use_animation = "eat"
			use_sound = "food_consume"
		"elixir":
			stack_size = 3
			use_animation = "drink"
			use_sound = "elixir_drink"
			particle_effect = "mystical_glow"

## Core Usage System
func can_use() -> bool:
	if not super.can_use():
		return false

	# Check cooldown (this will auto-clear expired cooldowns)
	if get_cooldown_remaining() > 0.0:
		return false

	# Check combat restrictions
	if not combat_usable and _is_player_in_combat():
		return false

	# Check if player meets requirements
	return _meets_usage_requirements()

func _perform_use_action() -> bool:
	if not can_use():
		return false
	
	# Start cooldown
	_start_cooldown()
	
	# Play effects
	_play_use_effects()
	
	# Apply effects
	var success = _apply_consumable_effects()
	
	if success:
		EventBus.consumable_used.emit(self)
	
	return success

func _apply_consumable_effects() -> bool:
	# Since Items are Resources, the actual effect application should be handled
	# by the system using the item (like Player or InventoryManager)
	# For now, we'll emit events and let the EventBus handle the effects

	# Apply instant effects through events
	_apply_instant_effects_via_events()

	# Apply over-time effects through events
	_apply_over_time_effects_via_events()

	# Apply buff effects through events
	_apply_buff_effects_via_events()

	return true

## Effect Application (Event-Based)
func _apply_instant_effects_via_events() -> void:
	# Health restoration
	if heal_amount > 0:
		var actual_heal = _calculate_heal_amount()
		EventBus.player_healed.emit(actual_heal, "consumable")

	# Mana restoration
	if mana_amount > 0:
		var actual_mana = _calculate_mana_amount()
		EventBus.player_mana_restored.emit(actual_mana, "consumable")

	# Custom instant effects
	for effect_name in instant_effects:
		var effect_value = instant_effects[effect_name]
		_apply_instant_effect_via_events(effect_name, effect_value)

func _apply_instant_effect_via_events(effect_name: String, value: float) -> void:
	match effect_name:
		"experience":
			EventBus.experience_gained.emit(int(value))
		"gold":
			EventBus.currency_gained.emit("gold", int(value))
		"teleport_to_town":
			EventBus.player_teleported.emit("town")
		"full_heal":
			EventBus.player_healed.emit(9999, "consumable")
		"remove_debuffs":
			EventBus.status_effects_cleared.emit("debuff")

func _apply_over_time_effects_via_events() -> void:
	for effect_name in over_time_effects:
		var effect_data = over_time_effects[effect_name]
		_start_over_time_effect_via_events(effect_name, effect_data)

func _start_over_time_effect_via_events(effect_name: String, effect_data: Dictionary) -> void:
	var duration = effect_data.get("duration", 10.0)
	var tick_rate = effect_data.get("tick_rate", 1.0)
	var value_per_tick = effect_data.get("value", 1.0)

	match effect_name:
		"health_over_time":
			EventBus.status_effect_applied.emit(null, "health_regen", duration, value_per_tick)
		"mana_over_time":
			EventBus.status_effect_applied.emit(null, "mana_regen", duration, value_per_tick)
		"poison":
			EventBus.status_effect_applied.emit(null, "poison", duration, value_per_tick)
		"burning":
			EventBus.status_effect_applied.emit(null, "burning", duration, value_per_tick)

func _apply_buff_effects_via_events() -> void:
	if buff_duration <= 0.0 or buff_effects.is_empty():
		return

	for stat_type in buff_effects:
		var buff_value = buff_effects[stat_type]
		EventBus.temporary_stat_applied.emit(null, stat_type, buff_value, buff_duration)

## Calculation Methods
func _calculate_heal_amount() -> int:
	# Return base heal amount
	# Player bonuses should be applied by the system that processes the healing
	return heal_amount

func _calculate_mana_amount() -> int:
	# Return base mana amount
	# Player bonuses should be applied by the system that processes the mana restoration
	return mana_amount

## Cooldown System
func _start_cooldown() -> void:
	if cooldown_time > 0.0:
		is_on_cooldown = true
		last_use_time = Time.get_time_dict_from_system()["unix"]

		# Note: Since Items are Resources, cooldown management should be handled
		# by the system that uses the item (like InventoryManager or Player)
		# For now, we'll use a simple time-based check in get_cooldown_remaining()

func _end_cooldown() -> void:
	is_on_cooldown = false

func get_cooldown_remaining() -> float:
	if not is_on_cooldown:
		return 0.0

	var current_time = Time.get_time_dict_from_system()["unix"]
	var elapsed = current_time - last_use_time
	var remaining = max(0.0, cooldown_time - elapsed)

	# Auto-clear cooldown when time expires
	if remaining <= 0.0:
		is_on_cooldown = false

	return remaining

## Audio/Visual Effects
func _play_use_effects() -> void:
	var player_position = _get_player_position()
	
	# Play sound effect
	if not use_sound.is_empty():
		EventBus.audio_requested.emit(use_sound, player_position)
	
	# Play particle effect
	if not particle_effect.is_empty():
		EventBus.effect_requested.emit(particle_effect, player_position)
	
	# Play animation
	if not use_animation.is_empty():
		EventBus.animation_requested.emit("player", use_animation)

## Utility Functions
func _is_player_in_combat() -> bool:
	# This would check with CombatManager or similar
	return false  # Placeholder

func _meets_usage_requirements() -> bool:
	# Override in subclasses for specific requirements
	return true

func _get_player_reference() -> Node:
	# Note: Items are Resources and don't have access to the scene tree
	# Player reference should be passed by the system using the item
	return null

func _get_player_stats() -> Node:
	# Note: Player stats should be accessed through the system using the item
	return null

func _get_player_position() -> Vector3:
	# Note: Player position should be provided by the system using the item
	return Vector3.ZERO

## Special Consumable Types
func create_health_potion(heal_value: int, potion_tier: int = 1) -> void:
	consumable_type = "potion"
	heal_amount = heal_value
	name = "Health Potion"
	description = "Restores %d health" % heal_value
	
	match potion_tier:
		1:
			name = "Minor Health Potion"
			sell_value = 5
		2:
			name = "Health Potion"
			sell_value = 15
		3:
			name = "Greater Health Potion"
			sell_value = 35
		4:
			name = "Superior Health Potion"
			sell_value = 75

func create_buff_scroll(stat_type: GameEnums.StatType, buff_value: float, duration: float) -> void:
	consumable_type = "scroll"
	buff_duration = duration
	buff_effects[stat_type] = buff_value
	
	var stat_name = GameEnums.StatType.keys()[stat_type].replace("_", " ").capitalize()
	name = "Scroll of %s" % stat_name
	description = "Increases %s by %.1f for %.0f seconds" % [stat_name.to_lower(), buff_value, duration]

func create_special_elixir(effect_name: String, effect_data: Dictionary) -> void:
	consumable_type = "elixir"
	instant_effects[effect_name] = effect_data.get("value", 1.0)
	
	name = "Elixir of %s" % effect_name.replace("_", " ").capitalize()
	description = effect_data.get("description", "A mysterious elixir with unknown effects")

## Tooltip Override
func get_tooltip_text() -> String:
	var tooltip = super.get_tooltip_text()
	
	# Add consumable-specific info
	tooltip += "\nType: %s" % consumable_type.capitalize()
	
	if heal_amount > 0:
		tooltip += "\nHeals: %d HP" % _calculate_heal_amount()
	
	if mana_amount > 0:
		tooltip += "\nRestores: %d MP" % _calculate_mana_amount()
	
	if buff_duration > 0.0:
		tooltip += "\nBuff Duration: %.1fs" % buff_duration
	
	if cooldown_time > 0.0:
		tooltip += "\nCooldown: %.1fs" % cooldown_time
		if is_on_cooldown:
			tooltip += " (%.1fs remaining)" % get_cooldown_remaining()
	
	if not combat_usable:
		tooltip += "\n[color=red]Cannot use in combat[/color]"
	
	return tooltip
