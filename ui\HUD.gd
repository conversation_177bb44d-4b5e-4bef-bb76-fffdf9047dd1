class_name HUD
extends Control

## Heads-up display for in-game UI elements
## Manages health/mana bars, skill buttons, and notifications

# UI References
@onready var health_bar: ProgressBar = $TopPanel/HealthBar
@onready var health_label: Label = $TopPanel/HealthBar/HealthLabel
@onready var mana_bar: ProgressBar = $TopPanel/ManaBar
@onready var mana_label: Label = $TopPanel/ManaBar/ManaLabel
@onready var level_label: Label = $TopPanel/LevelLabel
@onready var wave_label: Label = $WaveInfo/WaveLabel

# Skill buttons
@onready var skill1_button: Button = $SkillButtons/Skill1Button
@onready var skill2_button: Button = $SkillButtons/Skill2Button
@onready var dash_button: Button = $SkillButtons/DashButton

# Virtual joystick
@onready var joystick_base: Panel = $VirtualJoystick/JoystickBase
@onready var joystick_knob: Panel = $VirtualJoystick/JoystickKnob
@onready var notification_area: VBoxContainer = $NotificationArea

# State
var is_joystick_visible: bool = false
var notification_queue: Array[Dictionary] = []

func _ready() -> void:
	_setup_ui()
	_connect_events()
	_setup_skill_buttons()
	_setup_virtual_joystick()

func _setup_ui() -> void:
	# Configure UI scaling for mobile
	if OS.has_feature("mobile"):
		scale = Vector2.ONE * GameConstants.UI_SCALE_MOBILE
	
	# Set initial values
	_update_health_display(100, 100)
	_update_mana_display(50, 50)
	_update_level_display(1)
	_update_wave_display(1, 20)

func _connect_events() -> void:
	# Player stat events
	EventBus.player_health_changed.connect(_on_health_changed)
	EventBus.player_level_up.connect(_on_level_changed)
	EventBus.wave_started.connect(_on_wave_started)
	EventBus.wave_completed.connect(_on_wave_completed)
	EventBus.notification_shown.connect(_on_notification_requested)
	
	# Virtual joystick events
	EventBus.connect("virtual_joystick_started", _on_joystick_started)
	EventBus.connect("virtual_joystick_moved", _on_joystick_moved)
	EventBus.connect("virtual_joystick_ended", _on_joystick_ended)

func _setup_skill_buttons() -> void:
	# Connect skill button signals
	skill1_button.pressed.connect(_on_skill_button_pressed.bind("skill_1"))
	skill2_button.pressed.connect(_on_skill_button_pressed.bind("skill_2"))
	dash_button.pressed.connect(_on_skill_button_pressed.bind("dash"))
	
	# Style skill buttons for mobile
	var button_style = StyleBoxFlat.new()
	button_style.bg_color = Color(0.2, 0.2, 0.2, 0.8)
	button_style.corner_radius_top_left = 10
	button_style.corner_radius_top_right = 10
	button_style.corner_radius_bottom_left = 10
	button_style.corner_radius_bottom_right = 10
	
	for button in [skill1_button, skill2_button, dash_button]:
		button.add_theme_stylebox_override("normal", button_style)
		button.add_theme_stylebox_override("pressed", button_style)

func _setup_virtual_joystick() -> void:
	# Style joystick elements
	var base_style = StyleBoxFlat.new()
	base_style.bg_color = Color(0.3, 0.3, 0.3, 0.5)
	base_style.corner_radius_top_left = 75
	base_style.corner_radius_top_right = 75
	base_style.corner_radius_bottom_left = 75
	base_style.corner_radius_bottom_right = 75
	joystick_base.add_theme_stylebox_override("panel", base_style)
	
	var knob_style = StyleBoxFlat.new()
	knob_style.bg_color = Color(0.8, 0.8, 0.8, 0.8)
	knob_style.corner_radius_top_left = 25
	knob_style.corner_radius_top_right = 25
	knob_style.corner_radius_bottom_left = 25
	knob_style.corner_radius_bottom_right = 25
	joystick_knob.add_theme_stylebox_override("panel", knob_style)
	
	# Initially hide joystick
	_hide_virtual_joystick()

## Health and Mana Display
func _update_health_display(current: int, maximum: int) -> void:
	if health_bar and health_label:
		health_bar.max_value = maximum
		health_bar.value = current
		health_label.text = "%d/%d" % [current, maximum]
		
		# Color coding based on health percentage
		var health_percent = float(current) / float(maximum)
		if health_percent > 0.6:
			health_bar.modulate = Color.GREEN
		elif health_percent > 0.3:
			health_bar.modulate = Color.YELLOW
		else:
			health_bar.modulate = Color.RED

func _update_mana_display(current: int, maximum: int) -> void:
	if mana_bar and mana_label:
		mana_bar.max_value = maximum
		mana_bar.value = current
		mana_label.text = "%d/%d" % [current, maximum]
		mana_bar.modulate = Color.CYAN

func _update_level_display(level: int) -> void:
	if level_label:
		level_label.text = "Level %d" % level

func _update_wave_display(current_wave: int, total_waves: int) -> void:
	if wave_label:
		wave_label.text = "Wave %d/%d" % [current_wave, total_waves]

## Virtual Joystick Management
func _show_virtual_joystick() -> void:
	if not OS.has_feature("mobile"):
		return
	
	joystick_base.visible = true
	joystick_knob.visible = true
	is_joystick_visible = true

func _hide_virtual_joystick() -> void:
	joystick_base.visible = false
	joystick_knob.visible = false
	is_joystick_visible = false

func _update_joystick_knob_position(direction: Vector2) -> void:
	if not is_joystick_visible:
		return
	
	var base_center = joystick_base.size / 2
	var max_distance = joystick_base.size.x / 2 - joystick_knob.size.x / 2
	var knob_offset = direction * max_distance
	
	joystick_knob.position = base_center + knob_offset - joystick_knob.size / 2

## Notification System
func show_notification(message: String, type: String = "info", duration: float = 3.0) -> void:
	var notification = {
		"message": message,
		"type": type,
		"duration": duration,
		"timestamp": Time.get_unix_time_from_system()
	}
	
	notification_queue.append(notification)
	_process_notification_queue()

func _process_notification_queue() -> void:
	if notification_queue.is_empty():
		return
	
	var notification = notification_queue.pop_front()
	_display_notification(notification)

func _display_notification(notification: Dictionary) -> void:
	var label = Label.new()
	label.text = notification.message
	label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	
	# Style based on notification type
	match notification.type:
		"success":
			label.modulate = Color.GREEN
		"warning":
			label.modulate = Color.YELLOW
		"error":
			label.modulate = Color.RED
		"achievement":
			label.modulate = Color.GOLD
		"loot":
			label.modulate = Color.PURPLE
		_:
			label.modulate = Color.WHITE
	
	notification_area.add_child(label)
	
	# Animate notification
	var tween = create_tween()
	tween.parallel().tween_property(label, "modulate:a", 0.0, 0.5)
	tween.parallel().tween_property(label, "position:y", label.position.y - 50, 0.5)
	
	# Remove after duration
	get_tree().create_timer(notification.duration).timeout.connect(func():
		if is_instance_valid(label):
			label.queue_free()
	)

## Skill Button Cooldown Display
func set_skill_cooldown(skill_id: String, cooldown_time: float) -> void:
	var button: Button = null
	
	match skill_id:
		"skill_1":
			button = skill1_button
		"skill_2":
			button = skill2_button
		"dash":
			button = dash_button
	
	if button:
		_animate_skill_cooldown(button, cooldown_time)

func _animate_skill_cooldown(button: Button, cooldown_time: float) -> void:
	button.disabled = true
	button.modulate = Color(0.5, 0.5, 0.5, 1.0)
	
	var tween = create_tween()
	tween.tween_property(button, "modulate", Color.WHITE, cooldown_time)
	tween.tween_callback(func(): button.disabled = false)

## Event Handlers
func _on_health_changed(current: int, maximum: int) -> void:
	_update_health_display(current, maximum)

func _on_mana_changed(current: int, maximum: int) -> void:
	_update_mana_display(current, maximum)

func _on_level_changed(new_level: int) -> void:
	_update_level_display(new_level)
	show_notification("Level Up! Level %d" % new_level, "achievement")

func _on_wave_started(wave_number: int) -> void:
	_update_wave_display(wave_number, GameConstants.WAVES_PER_RUN)
	show_notification("Wave %d Started!" % wave_number, "info")

func _on_wave_completed(wave_number: int) -> void:
	show_notification("Wave %d Completed!" % wave_number, "success")

func _on_notification_requested(message: String, type: String) -> void:
	show_notification(message, type)

func _on_skill_button_pressed(skill_id: String) -> void:
	EventBus.skill_used.emit(skill_id, 0.0)
	
	# Add haptic feedback on mobile
	if OS.has_feature("mobile"):
		Input.vibrate_handheld(50)

func _on_joystick_started(position: Vector2) -> void:
	_show_virtual_joystick()

func _on_joystick_moved(direction: Vector2) -> void:
	_update_joystick_knob_position(direction)

func _on_joystick_ended() -> void:
	_hide_virtual_joystick()

## Utility Functions
func get_screen_center() -> Vector2:
	return get_viewport().get_visible_rect().size / 2

func is_point_in_ui(point: Vector2) -> bool:
	# Check if a screen point is over UI elements
	var ui_rects = [
		$TopPanel.get_rect(),
		$WaveInfo.get_rect(),
		$SkillButtons.get_rect()
	]
	
	for rect in ui_rects:
		if rect.has_point(point):
			return true
	
	return false

func set_ui_visibility(visible: bool) -> void:
	self.visible = visible

## Debug Functions
func debug_show_all_notifications() -> void:
	show_notification("Test Info", "info")
	show_notification("Test Success", "success")
	show_notification("Test Warning", "warning")
	show_notification("Test Error", "error")
	show_notification("Test Achievement", "achievement")
	show_notification("Test Loot", "loot")

func debug_test_skill_cooldowns() -> void:
	set_skill_cooldown("skill_1", 3.0)
	set_skill_cooldown("skill_2", 5.0)
	set_skill_cooldown("dash", 2.0)
