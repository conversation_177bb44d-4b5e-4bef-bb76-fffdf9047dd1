class_name Armor
extends Item

## Armor class implementing defensive equipment functionality
## Handles damage reduction, armor types, and defensive bonuses

@export var armor_type: GameEnums.ArmorType = GameEnums.ArmorType.LIGHT
@export var armor_value: int = 5
@export var magic_resistance: float = 0.0
@export var movement_penalty: float = 0.0
@export var set_bonus_id: String = ""

# Armor-specific properties
@export var damage_reduction: float = 0.0
@export var elemental_resistances: Dictionary = {}
@export var status_immunities: Array[GameEnums.StatusEffect] = []

func _init() -> void:
	super._init()
	item_type = GameEnums.ItemType.ARMOR

func _setup_base_properties() -> void:
	super._setup_base_properties()
	_setup_armor_defaults()
	_setup_resistances()

func _setup_armor_defaults() -> void:
	match armor_type:
		GameEnums.ArmorType.LIGHT:
			armor_value = 5
			magic_resistance = 0.1
			movement_penalty = 0.0
			damage_reduction = 0.05
		
		GameEnums.ArmorType.MEDIUM:
			armor_value = 10
			magic_resistance = 0.05
			movement_penalty = 0.1
			damage_reduction = 0.1
		
		GameEnums.ArmorType.HEAVY:
			armor_value = 20
			magic_resistance = 0.0
			movement_penalty = 0.2
			damage_reduction = 0.2
		
		GameEnums.ArmorType.ROBE:
			armor_value = 2
			magic_resistance = 0.3
			movement_penalty = 0.0
			damage_reduction = 0.02

func _setup_resistances() -> void:
	# Initialize elemental resistances based on armor type
	elemental_resistances = {
		"fire": 0.0,
		"ice": 0.0,
		"lightning": 0.0,
		"poison": 0.0,
		"dark": 0.0,
		"light": 0.0
	}
	
	# Set type-specific resistances
	match armor_type:
		GameEnums.ArmorType.HEAVY:
			elemental_resistances["fire"] = 0.1
			elemental_resistances["ice"] = 0.1
		
		GameEnums.ArmorType.ROBE:
			elemental_resistances["lightning"] = 0.2
			elemental_resistances["dark"] = 0.15

## Defense Calculations
func calculate_damage_reduction(incoming_damage: int, damage_type: GameEnums.DamageType) -> Dictionary:
	var reduction_info = {
		"original_damage": incoming_damage,
		"armor_reduction": 0,
		"magic_reduction": 0,
		"elemental_reduction": 0,
		"final_damage": incoming_damage,
		"blocked": false
	}
	
	var final_damage = float(incoming_damage)
	
	# Apply armor reduction for physical damage
	if damage_type == GameEnums.DamageType.PHYSICAL:
		var armor_reduction = min(final_damage * 0.8, armor_value * 0.5)
		reduction_info.armor_reduction = int(armor_reduction)
		final_damage -= armor_reduction
	
	# Apply magic resistance for magical damage
	elif damage_type == GameEnums.DamageType.MAGICAL:
		var magic_reduction = final_damage * magic_resistance
		reduction_info.magic_reduction = int(magic_reduction)
		final_damage -= magic_reduction
	
	# Apply percentage damage reduction
	final_damage *= (1.0 - damage_reduction)
	
	# Ensure minimum damage
	final_damage = max(1, final_damage)
	reduction_info.final_damage = int(final_damage)
	
	return reduction_info

func get_total_armor_value() -> int:
	var total = armor_value
	total += get_stat_value(GameEnums.StatType.DEFENSE)
	
	# Add socketed rune bonuses
	for rune in socketed_runes:
		if rune != null and rune.has_method("get_armor_bonus"):
			total += rune.get_armor_bonus()
	
	return total

func get_elemental_resistance(element: String) -> float:
	return elemental_resistances.get(element, 0.0)

func get_status_immunity(status: GameEnums.StatusEffect) -> bool:
	return status in status_immunities

## Set Bonus System
func has_set_bonus() -> bool:
	return not set_bonus_id.is_empty()

func get_set_bonus_id() -> String:
	return set_bonus_id

func apply_set_bonus(pieces_equipped: int) -> Dictionary:
	if not has_set_bonus():
		return {}
	
	var bonus = {}
	
	# Example set bonuses based on pieces equipped
	match set_bonus_id:
		"guardian_set":
			if pieces_equipped >= 2:
				bonus[GameEnums.StatType.HEALTH] = 50
			if pieces_equipped >= 3:
				bonus[GameEnums.StatType.DEFENSE] = 10
			if pieces_equipped >= 4:
				bonus["damage_reflection"] = 0.15
		
		"mage_set":
			if pieces_equipped >= 2:
				bonus[GameEnums.StatType.MANA] = 30
			if pieces_equipped >= 3:
				bonus["spell_power"] = 0.2
			if pieces_equipped >= 4:
				bonus["mana_regeneration"] = 2.0
		
		"assassin_set":
			if pieces_equipped >= 2:
				bonus[GameEnums.StatType.CRIT_CHANCE] = 0.1
			if pieces_equipped >= 3:
				bonus[GameEnums.StatType.MOVE_SPEED] = 1.0
			if pieces_equipped >= 4:
				bonus["stealth_duration"] = 2.0
	
	return bonus

## Equipment Overrides
func _apply_equipment_effects() -> void:
	super._apply_equipment_effects()
	_apply_armor_effects()

func _remove_equipment_effects() -> void:
	super._remove_equipment_effects()
	_remove_armor_effects()

func _apply_armor_effects() -> void:
	# Apply movement penalty
	if movement_penalty > 0:
		var speed_reduction = {GameEnums.StatType.MOVE_SPEED: -movement_penalty}
		EventBus.player_stats_changed.emit(speed_reduction)
	
	# Apply status immunities
	for immunity in status_immunities:
		EventBus.emit_signal("status_immunity_applied", immunity)

func _remove_armor_effects() -> void:
	# Remove movement penalty
	if movement_penalty > 0:
		var speed_restoration = {GameEnums.StatType.MOVE_SPEED: movement_penalty}
		EventBus.player_stats_changed.emit(speed_restoration)
	
	# Remove status immunities
	for immunity in status_immunities:
		EventBus.emit_signal("status_immunity_removed", immunity)

## Special Armor Abilities
func trigger_armor_ability(trigger_type: String) -> bool:
	match armor_type:
		GameEnums.ArmorType.LIGHT:
			if trigger_type == "dodge":
				return _trigger_evasion_boost()
		
		GameEnums.ArmorType.MEDIUM:
			if trigger_type == "hit":
				return _trigger_balanced_defense()
		
		GameEnums.ArmorType.HEAVY:
			if trigger_type == "hit":
				return _trigger_damage_reflection()
		
		GameEnums.ArmorType.ROBE:
			if trigger_type == "spell_cast":
				return _trigger_mana_shield()
	
	return false

func _trigger_evasion_boost() -> bool:
	# Light armor: Temporary speed boost after dodging
	EventBus.effect_requested.emit("evasion_boost", Vector3.ZERO)
	return true

func _trigger_balanced_defense() -> bool:
	# Medium armor: Balanced defensive response
	EventBus.effect_requested.emit("balanced_defense", Vector3.ZERO)
	return true

func _trigger_damage_reflection() -> bool:
	# Heavy armor: Reflect portion of damage back to attacker
	EventBus.effect_requested.emit("damage_reflection", Vector3.ZERO)
	return true

func _trigger_mana_shield() -> bool:
	# Robe: Convert damage to mana loss
	EventBus.effect_requested.emit("mana_shield", Vector3.ZERO)
	return true

## Visual and Audio
func get_armor_material_sound() -> String:
	match armor_type:
		GameEnums.ArmorType.LIGHT:
			return "leather_hit"
		GameEnums.ArmorType.MEDIUM:
			return "chainmail_hit"
		GameEnums.ArmorType.HEAVY:
			return "plate_hit"
		GameEnums.ArmorType.ROBE:
			return "cloth_hit"
		_:
			return "default_hit"

func get_armor_visual_effect() -> String:
	match armor_type:
		GameEnums.ArmorType.LIGHT:
			return "leather_gleam"
		GameEnums.ArmorType.MEDIUM:
			return "metal_shine"
		GameEnums.ArmorType.HEAVY:
			return "plate_reflection"
		GameEnums.ArmorType.ROBE:
			return "magical_aura"
		_:
			return "default_armor"

## Utility Functions
func get_armor_type_name() -> String:
	return GameEnums.ArmorType.keys()[armor_type]

func get_equipment_slot() -> String:
	return "armor"

func is_heavy_armor() -> bool:
	return armor_type == GameEnums.ArmorType.HEAVY

func is_magical_armor() -> bool:
	return armor_type == GameEnums.ArmorType.ROBE

## Tooltip Override
func get_tooltip_text() -> String:
	var tooltip = super.get_tooltip_text()
	
	# Add armor-specific information
	tooltip += "\n[b]Armor Type:[/b] %s" % get_armor_type_name()
	tooltip += "\n[b]Armor Value:[/b] %d" % armor_value
	
	if magic_resistance > 0:
		tooltip += "\n[b]Magic Resistance:[/b] %.1f%%" % (magic_resistance * 100)
	
	if movement_penalty > 0:
		tooltip += "\n[b]Movement Penalty:[/b] -%.1f" % movement_penalty
	
	if damage_reduction > 0:
		tooltip += "\n[b]Damage Reduction:[/b] %.1f%%" % (damage_reduction * 100)
	
	# Add elemental resistances
	var has_resistances = false
	for element in elemental_resistances:
		if elemental_resistances[element] > 0:
			if not has_resistances:
				tooltip += "\n[b]Elemental Resistances:[/b]"
				has_resistances = true
			tooltip += "\n  • %s: %.1f%%" % [element.capitalize(), elemental_resistances[element] * 100]
	
	# Add status immunities
	if not status_immunities.is_empty():
		tooltip += "\n[b]Status Immunities:[/b]"
		for immunity in status_immunities:
			tooltip += "\n  • %s" % GameEnums.StatusEffect.keys()[immunity]
	
	# Add set bonus info
	if has_set_bonus():
		tooltip += "\n[b]Set:[/b] %s" % set_bonus_id.replace("_", " ").capitalize()
	
	return tooltip

## Serialization Override
func to_dict() -> Dictionary:
	var data = super.to_dict()
	data["armor_type"] = armor_type
	data["armor_value"] = armor_value
	data["magic_resistance"] = magic_resistance
	data["movement_penalty"] = movement_penalty
	data["damage_reduction"] = damage_reduction
	data["elemental_resistances"] = elemental_resistances
	data["status_immunities"] = status_immunities
	data["set_bonus_id"] = set_bonus_id
	return data

func from_dict(data: Dictionary) -> void:
	super.from_dict(data)
	armor_type = data.get("armor_type", GameEnums.ArmorType.LIGHT)
	armor_value = data.get("armor_value", 5)
	magic_resistance = data.get("magic_resistance", 0.0)
	movement_penalty = data.get("movement_penalty", 0.0)
	damage_reduction = data.get("damage_reduction", 0.0)
	elemental_resistances = data.get("elemental_resistances", {})
	status_immunities = data.get("status_immunities", [])
	set_bonus_id = data.get("set_bonus_id", "")
