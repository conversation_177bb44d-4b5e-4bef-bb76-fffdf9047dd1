class_name Wings
extends Item

## Wings class implementing flight mechanics and mobility bonuses
## Provides unique movement abilities and visual customization

@export var flight_duration: float = GameConstants.FLIGHT_DURATION_BASE
@export var flight_speed_multiplier: float = GameConstants.WING_SPEED_MULTIPLIER
@export var wing_style: String = "feather"
@export var wing_color: Color = Color.WHITE
@export var particle_effect: String = ""

# Wing-specific abilities
@export var special_abilities: Array[String] = []
@export var glide_efficiency: float = 1.0
@export var air_dash_count: int = 0

func _init() -> void:
	super._init()
	item_type = GameEnums.ItemType.WINGS

func _setup_base_properties() -> void:
	super._setup_base_properties()
	_setup_wing_defaults()

func _setup_wing_defaults() -> void:
	# Base wing properties
	socket_count = GameConstants.WING_SOCKET_COUNT
	
	# Default stats for wings
	stat_modifiers[GameEnums.StatType.MOVE_SPEED] = 1.0

## Flight System Integration
func get_flight_duration() -> float:
	var base_duration = flight_duration
	
	# Add bonuses from socketed runes
	for rune in socketed_runes:
		if rune != null and rune.has_method("get_flight_bonus"):
			base_duration += rune.get_flight_bonus()
	
	return base_duration

func get_flight_speed_multiplier() -> float:
	var multiplier = flight_speed_multiplier
	
	# Add speed bonuses from stats
	var speed_bonus = get_stat_value(GameEnums.StatType.MOVE_SPEED)
	multiplier += speed_bonus * 0.1  # 10% per move speed point
	
	return multiplier

func can_perform_air_dash() -> bool:
	return air_dash_count > 0

func consume_air_dash() -> bool:
	if air_dash_count > 0:
		air_dash_count -= 1
		return true
	return false

func reset_air_dashes() -> void:
	# Reset air dashes when landing or at start of flight
	match wing_style:
		"feather":
			air_dash_count = 1
		"dragon":
			air_dash_count = 2
		"phoenix":
			air_dash_count = 3
		"celestial":
			air_dash_count = 5
		_:
			air_dash_count = 1

## Wing Abilities
func activate_wing_ability(ability_name: String) -> bool:
	if ability_name not in special_abilities:
		return false
	
	match ability_name:
		"wind_burst":
			return _activate_wind_burst()
		"feather_storm":
			return _activate_feather_storm()
		"phoenix_dive":
			return _activate_phoenix_dive()
		"celestial_ascension":
			return _activate_celestial_ascension()
		"shadow_wings":
			return _activate_shadow_wings()
	
	return false

func _activate_wind_burst() -> bool:
	# Create wind burst that pushes enemies away
	EventBus.effect_requested.emit("wind_burst", Vector3.ZERO)
	return true

func _activate_feather_storm() -> bool:
	# Rain of feathers that deal damage
	EventBus.effect_requested.emit("feather_storm", Vector3.ZERO)
	return true

func _activate_phoenix_dive() -> bool:
	# Diving attack with fire damage
	EventBus.effect_requested.emit("phoenix_dive", Vector3.ZERO)
	return true

func _activate_celestial_ascension() -> bool:
	# Temporary invincibility and healing
	EventBus.effect_requested.emit("celestial_ascension", Vector3.ZERO)
	return true

func _activate_shadow_wings() -> bool:
	# Temporary invisibility
	EventBus.effect_requested.emit("shadow_wings", Vector3.ZERO)
	return true

## Visual Customization
func get_wing_material_path() -> String:
	return "res://materials/wings/%s_wings.tres" % wing_style

func get_particle_effect_path() -> String:
	if particle_effect.is_empty():
		return "res://vfx/wings/%s_particles.tscn" % wing_style
	return "res://vfx/wings/%s.tscn" % particle_effect

func get_wing_animation_set() -> String:
	return "res://animations/wings/%s_animations.tres" % wing_style

func apply_wing_visuals(wing_node: Node3D) -> void:
	if not wing_node:
		return
	
	# Apply material
	var mesh_instance = wing_node.get_node("MeshInstance3D")
	if mesh_instance:
		var material = load(get_wing_material_path())
		if material:
			mesh_instance.material_override = material
			mesh_instance.material_override.albedo_color = wing_color
	
	# Apply particle effects
	var particles = wing_node.get_node("ParticleSystem3D")
	if particles:
		var particle_material = load(get_particle_effect_path())
		if particle_material:
			particles.process_material = particle_material

## Equipment Overrides
func _apply_equipment_effects() -> void:
	super._apply_equipment_effects()
	_apply_wing_effects()

func _remove_equipment_effects() -> void:
	super._remove_equipment_effects()
	_remove_wing_effects()

func _apply_wing_effects() -> void:
	# Enable flight capability
	EventBus.emit_signal("flight_enabled", true)
	
	# Apply special abilities
	for ability in special_abilities:
		EventBus.emit_signal("wing_ability_unlocked", ability)
	
	# Reset air dashes
	reset_air_dashes()

func _remove_wing_effects() -> void:
	# Disable flight capability
	EventBus.emit_signal("flight_enabled", false)
	
	# Remove special abilities
	for ability in special_abilities:
		EventBus.emit_signal("wing_ability_locked", ability)

## Wing Upgrade System
func upgrade_wing_tier() -> bool:
	# Upgrade wing to next tier
	match wing_style:
		"feather":
			wing_style = "enhanced_feather"
			flight_duration += 1.0
			special_abilities.append("wind_burst")
		"enhanced_feather":
			wing_style = "dragon"
			flight_duration += 2.0
			flight_speed_multiplier += 0.2
			special_abilities.append("feather_storm")
		"dragon":
			wing_style = "phoenix"
			flight_duration += 3.0
			special_abilities.append("phoenix_dive")
		"phoenix":
			wing_style = "celestial"
			flight_duration += 5.0
			flight_speed_multiplier += 0.5
			special_abilities.append("celestial_ascension")
		_:
			return false  # Max tier reached
	
	# Update rarity
	if rarity < GameEnums.ItemRarity.DIVINE:
		rarity += 1
	
	# Recalculate stats
	_setup_stat_modifiers()
	return true

func can_upgrade() -> bool:
	return wing_style != "celestial"

## Utility Functions
func get_wing_style_name() -> String:
	return wing_style.replace("_", " ").capitalize()

func get_equipment_slot() -> String:
	return "wings"

func is_legendary_wings() -> bool:
	return rarity >= GameEnums.ItemRarity.LEGENDARY

func get_wing_tier() -> int:
	match wing_style:
		"feather":
			return 1
		"enhanced_feather":
			return 2
		"dragon":
			return 3
		"phoenix":
			return 4
		"celestial":
			return 5
		_:
			return 1

## Tooltip Override
func get_tooltip_text() -> String:
	var tooltip = super.get_tooltip_text()
	
	# Add wing-specific information
	tooltip += "\n[b]Wing Style:[/b] %s" % get_wing_style_name()
	tooltip += "\n[b]Flight Duration:[/b] %.1fs" % get_flight_duration()
	tooltip += "\n[b]Flight Speed:[/b] +%.0f%%" % ((get_flight_speed_multiplier() - 1.0) * 100)
	
	if air_dash_count > 0:
		tooltip += "\n[b]Air Dashes:[/b] %d" % air_dash_count
	
	if glide_efficiency != 1.0:
		tooltip += "\n[b]Glide Efficiency:[/b] %.0f%%" % (glide_efficiency * 100)
	
	# Add special abilities
	if not special_abilities.is_empty():
		tooltip += "\n[b]Special Abilities:[/b]"
		for ability in special_abilities:
			var ability_name = ability.replace("_", " ").capitalize()
			tooltip += "\n  • %s" % ability_name
	
	return tooltip

## Serialization Override
func to_dict() -> Dictionary:
	var data = super.to_dict()
	data["flight_duration"] = flight_duration
	data["flight_speed_multiplier"] = flight_speed_multiplier
	data["wing_style"] = wing_style
	data["wing_color"] = wing_color.to_html()
	data["particle_effect"] = particle_effect
	data["special_abilities"] = special_abilities
	data["glide_efficiency"] = glide_efficiency
	data["air_dash_count"] = air_dash_count
	return data

func from_dict(data: Dictionary) -> void:
	super.from_dict(data)
	flight_duration = data.get("flight_duration", GameConstants.FLIGHT_DURATION_BASE)
	flight_speed_multiplier = data.get("flight_speed_multiplier", GameConstants.WING_SPEED_MULTIPLIER)
	wing_style = data.get("wing_style", "feather")
	wing_color = Color.from_string(data.get("wing_color", "#ffffff"), Color.WHITE)
	particle_effect = data.get("particle_effect", "")
	special_abilities = data.get("special_abilities", [])
	glide_efficiency = data.get("glide_efficiency", 1.0)
	air_dash_count = data.get("air_dash_count", 0)
