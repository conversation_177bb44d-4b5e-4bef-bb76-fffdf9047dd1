class_name Weapon
extends Item

## Weapon class implementing weapon-specific functionality
## Handles attack patterns, weapon types, and combat mechanics

@export var weapon_type: GameEnums.WeaponType = GameEnums.WeaponType.SWORD
@export var attack_speed: float = 1.0
@export var range: float = GameConstants.AUTO_ATTACK_RANGE
@export var damage_type: GameEnums.DamageType = GameEnums.DamageType.PHYSICAL
@export var special_effects: Array[String] = []

# Weapon-specific stats
@export var base_damage: int = 10
@export var critical_bonus: float = 0.0
@export var accuracy: float = 1.0

# Attack patterns and animations
@export var attack_animations: Array[String] = []
@export var combo_count: int = 1
@export var combo_timing_window: float = 1.0

# Special weapon abilities
@export var weapon_skill_id: String = ""
@export var passive_effects: Dictionary = {}

func _init() -> void:
	super._init()
	item_type = GameEnums.ItemType.WEAPON

func _setup_base_properties() -> void:
	super._setup_base_properties()
	_setup_weapon_defaults()
	_setup_attack_patterns()

func _setup_weapon_defaults() -> void:
	match weapon_type:
		GameEnums.WeaponType.SWORD:
			attack_speed = 1.0
			range = 3.0
			combo_count = 3
			attack_animations = ["slash_1", "slash_2", "thrust"]
		
		GameEnums.WeaponType.STAFF:
			attack_speed = 0.8
			range = 4.0
			combo_count = 2
			damage_type = GameEnums.DamageType.MAGICAL
			attack_animations = ["cast_1", "cast_2"]
		
		GameEnums.WeaponType.BOW:
			attack_speed = 1.2
			range = 8.0
			combo_count = 1
			attack_animations = ["shoot"]
		
		GameEnums.WeaponType.DAGGER:
			attack_speed = 1.5
			range = 2.0
			combo_count = 4
			critical_bonus = 0.1
			attack_animations = ["stab_1", "stab_2", "slice", "backstab"]
		
		GameEnums.WeaponType.HAMMER:
			attack_speed = 0.6
			range = 3.5
			combo_count = 2
			attack_animations = ["smash", "overhead"]
		
		GameEnums.WeaponType.SPEAR:
			attack_speed = 0.9
			range = 5.0
			combo_count = 3
			attack_animations = ["thrust_1", "thrust_2", "sweep"]

func _setup_attack_patterns() -> void:
	# Initialize weapon-specific passive effects
	match weapon_type:
		GameEnums.WeaponType.SWORD:
			passive_effects["balanced_combat"] = true
		
		GameEnums.WeaponType.STAFF:
			passive_effects["mana_efficiency"] = 0.2
			passive_effects["spell_power"] = 0.15
		
		GameEnums.WeaponType.BOW:
			passive_effects["piercing_shot"] = true
			passive_effects["range_bonus"] = 2.0
		
		GameEnums.WeaponType.DAGGER:
			passive_effects["backstab_damage"] = 1.5
			passive_effects["crit_chance_bonus"] = 0.1
		
		GameEnums.WeaponType.HAMMER:
			passive_effects["area_damage"] = 0.5
			passive_effects["stun_chance"] = 0.15
		
		GameEnums.WeaponType.SPEAR:
			passive_effects["reach_advantage"] = true
			passive_effects["armor_penetration"] = 0.2

## Combat Functions
func calculate_damage(base_attack: int, target_defense: int = 0) -> Dictionary:
	var damage_info = {
		"base_damage": 0,
		"final_damage": 0,
		"damage_type": damage_type,
		"is_critical": false,
		"is_excellent": false,
		"is_double": false,
		"effects": []
	}
	
	# Calculate base damage
	var weapon_damage = get_stat_value(GameEnums.StatType.ATTACK)
	damage_info.base_damage = base_attack + weapon_damage + base_damage
	
	# Apply weapon-specific modifiers
	var final_damage = damage_info.base_damage
	
	# Apply passive effects
	if passive_effects.has("spell_power") and damage_type == GameEnums.DamageType.MAGICAL:
		final_damage *= (1.0 + passive_effects.spell_power)
	
	# Apply armor penetration
	var effective_defense = target_defense
	if passive_effects.has("armor_penetration"):
		effective_defense *= (1.0 - passive_effects.armor_penetration)
	
	# Calculate final damage after defense
	final_damage = max(1, final_damage - effective_defense)
	
	damage_info.final_damage = int(final_damage)
	return damage_info

func get_attack_speed_modifier() -> float:
	var base_speed = attack_speed
	var speed_bonus = get_stat_value(GameEnums.StatType.ATTACK_SPEED)
	return base_speed * (1.0 + speed_bonus)

func get_effective_range() -> float:
	var base_range = range
	if passive_effects.has("range_bonus"):
		base_range += passive_effects.range_bonus
	return base_range

func get_critical_chance_bonus() -> float:
	var bonus = critical_bonus
	if passive_effects.has("crit_chance_bonus"):
		bonus += passive_effects.crit_chance_bonus
	return bonus

## Special Abilities
func can_use_weapon_skill() -> bool:
	return not weapon_skill_id.is_empty() and is_equipped

func use_weapon_skill() -> bool:
	if not can_use_weapon_skill():
		return false
	
	# Trigger weapon-specific skill
	EventBus.skill_used.emit(weapon_skill_id, 0.0)
	_apply_weapon_skill_effect()
	return true

func _apply_weapon_skill_effect() -> void:
	match weapon_type:
		GameEnums.WeaponType.SWORD:
			_execute_twisting_slash()
		GameEnums.WeaponType.STAFF:
			_execute_mana_burst()
		GameEnums.WeaponType.BOW:
			_execute_multi_shot()
		GameEnums.WeaponType.DAGGER:
			_execute_shadow_strike()
		GameEnums.WeaponType.HAMMER:
			_execute_ground_slam()
		GameEnums.WeaponType.SPEAR:
			_execute_piercing_thrust()

func _execute_twisting_slash() -> void:
	# Sword special: Area damage around player
	EventBus.effect_requested.emit("twisting_slash", Vector3.ZERO)

func _execute_mana_burst() -> void:
	# Staff special: Restore mana and boost next spell
	EventBus.effect_requested.emit("mana_burst", Vector3.ZERO)

func _execute_multi_shot() -> void:
	# Bow special: Fire multiple projectiles
	EventBus.effect_requested.emit("multi_shot", Vector3.ZERO)

func _execute_shadow_strike() -> void:
	# Dagger special: Teleport behind enemy and critical strike
	EventBus.effect_requested.emit("shadow_strike", Vector3.ZERO)

func _execute_ground_slam() -> void:
	# Hammer special: AoE stun and damage
	EventBus.effect_requested.emit("ground_slam", Vector3.ZERO)

func _execute_piercing_thrust() -> void:
	# Spear special: Line attack that pierces through enemies
	EventBus.effect_requested.emit("piercing_thrust", Vector3.ZERO)

## Equipment Overrides
func _apply_equipment_effects() -> void:
	super._apply_equipment_effects()
	_apply_weapon_passives()

func _remove_equipment_effects() -> void:
	super._remove_equipment_effects()
	_remove_weapon_passives()

func _apply_weapon_passives() -> void:
	# Apply weapon-specific passive effects to player
	for effect in passive_effects:
		match effect:
			"mana_efficiency":
				# Reduce mana costs
				pass
			"backstab_damage":
				# Increase damage from behind
				pass
			"area_damage":
				# Add AoE to attacks
				pass

func _remove_weapon_passives() -> void:
	# Remove weapon-specific passive effects
	pass

## Animation and Visual Effects
func get_attack_animation(combo_index: int = 0) -> String:
	if attack_animations.is_empty():
		return "default_attack"
	
	var animation_index = combo_index % attack_animations.size()
	return attack_animations[animation_index]

func get_weapon_trail_color() -> Color:
	match weapon_type:
		GameEnums.WeaponType.SWORD:
			return Color.SILVER
		GameEnums.WeaponType.STAFF:
			return Color.CYAN
		GameEnums.WeaponType.BOW:
			return Color.BROWN
		GameEnums.WeaponType.DAGGER:
			return Color.DARK_GRAY
		GameEnums.WeaponType.HAMMER:
			return Color.ORANGE
		GameEnums.WeaponType.SPEAR:
			return Color.GOLD
		_:
			return Color.WHITE

func get_impact_effect() -> String:
	match weapon_type:
		GameEnums.WeaponType.SWORD:
			return "slash_impact"
		GameEnums.WeaponType.STAFF:
			return "magic_impact"
		GameEnums.WeaponType.BOW:
			return "arrow_impact"
		GameEnums.WeaponType.DAGGER:
			return "pierce_impact"
		GameEnums.WeaponType.HAMMER:
			return "crush_impact"
		GameEnums.WeaponType.SPEAR:
			return "thrust_impact"
		_:
			return "default_impact"

## Utility Functions
func get_weapon_type_name() -> String:
	return GameEnums.WeaponType.keys()[weapon_type]

func is_ranged_weapon() -> bool:
	return weapon_type == GameEnums.WeaponType.BOW

func is_magic_weapon() -> bool:
	return weapon_type == GameEnums.WeaponType.STAFF

func get_equipment_slot() -> String:
	return "weapon"

## Tooltip Override
func get_tooltip_text() -> String:
	var tooltip = super.get_tooltip_text()
	
	# Add weapon-specific information
	tooltip += "\n[b]Weapon Type:[/b] %s" % get_weapon_type_name()
	tooltip += "\n[b]Attack Speed:[/b] %.1f" % attack_speed
	tooltip += "\n[b]Range:[/b] %.1f" % range
	
	if combo_count > 1:
		tooltip += "\n[b]Combo Attacks:[/b] %d" % combo_count
	
	# Add passive effects
	if not passive_effects.is_empty():
		tooltip += "\n[b]Special Effects:[/b]"
		for effect in passive_effects:
			var effect_name = effect.replace("_", " ").capitalize()
			tooltip += "\n  • %s" % effect_name
	
	return tooltip

## Serialization Override
func to_dict() -> Dictionary:
	var data = super.to_dict()
	data["weapon_type"] = weapon_type
	data["attack_speed"] = attack_speed
	data["range"] = range
	data["damage_type"] = damage_type
	data["base_damage"] = base_damage
	data["critical_bonus"] = critical_bonus
	data["accuracy"] = accuracy
	data["combo_count"] = combo_count
	data["weapon_skill_id"] = weapon_skill_id
	data["passive_effects"] = passive_effects
	data["attack_animations"] = attack_animations
	return data

func from_dict(data: Dictionary) -> void:
	super.from_dict(data)
	weapon_type = data.get("weapon_type", GameEnums.WeaponType.SWORD)
	attack_speed = data.get("attack_speed", 1.0)
	range = data.get("range", GameConstants.AUTO_ATTACK_RANGE)
	damage_type = data.get("damage_type", GameEnums.DamageType.PHYSICAL)
	base_damage = data.get("base_damage", 10)
	critical_bonus = data.get("critical_bonus", 0.0)
	accuracy = data.get("accuracy", 1.0)
	combo_count = data.get("combo_count", 1)
	weapon_skill_id = data.get("weapon_skill_id", "")
	passive_effects = data.get("passive_effects", {})
	attack_animations = data.get("attack_animations", [])
