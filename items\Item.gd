class_name Item
extends Resource

## Base item class following the Strategy pattern for different item behaviors
## Implements the Template Method pattern for common item operations

@export var id: String = ""
@export var name: String = ""
@export var description: String = ""
@export var icon_path: String = ""
@export var item_type: GameEnums.ItemType = GameEnums.ItemType.CONSUMABLE
@export var rarity: GameEnums.ItemRarity = GameEnums.ItemRarity.COMMON
@export var level_requirement: int = 1
@export var stack_size: int = 1
@export var sell_value: int = 1
@export var is_tradeable: bool = true
@export var is_consumable: bool = false

# Base stats that all items can potentially have
@export var stat_modifiers: Dictionary = {}

# Socket system for gear enhancement
@export var socket_count: int = 0
@export var socketed_runes: Array[Resource] = []

# Item state
var current_stack: int = 1
var is_equipped: bool = false
var durability: int = 100
var max_durability: int = 100

# Signals for item events
signal item_used(item: Item)
signal item_equipped(item: Item)
signal item_unequipped(item: Item)
signal durability_changed(current: int, max: int)
signal socket_filled(socket_index: int, rune: Resource)
signal socket_emptied(socket_index: int)

func _init() -> void:
	_initialize_item()

func _initialize_item() -> void:
	# Template method - override in subclasses for specific initialization
	_setup_base_properties()
	_setup_stat_modifiers()
	_setup_sockets()

func _setup_base_properties() -> void:
	# Override in subclasses for specific property setup
	pass

func _setup_stat_modifiers() -> void:
	# Initialize empty stat modifiers dictionary
	if stat_modifiers.is_empty():
		for stat in GameEnums.StatType.values():
			stat_modifiers[stat] = 0

func _setup_sockets() -> void:
	# Initialize socket array based on socket count
	socketed_runes.resize(socket_count)
	for i in socket_count:
		socketed_runes[i] = null

## Core Item Operations
func use_item() -> bool:
	if not can_use():
		return false
	
	var success = _perform_use_action()
	if success:
		_handle_item_consumption()
		item_used.emit(self)
		EventBus.notification_shown.emit("Used %s" % name, "info")
	
	return success

func can_use() -> bool:
	# Template method - override in subclasses
	return is_consumable and current_stack > 0

func _perform_use_action() -> bool:
	# Override in subclasses for specific use behavior
	return true

func _handle_item_consumption() -> void:
	if is_consumable and current_stack > 0:
		current_stack -= 1

## Equipment System
func equip() -> bool:
	if not can_equip():
		return false
	
	is_equipped = true
	_apply_equipment_effects()
	item_equipped.emit(self)
	EventBus.item_equipped.emit(self, get_equipment_slot())
	
	return true

func unequip() -> bool:
	if not is_equipped:
		return false
	
	is_equipped = false
	_remove_equipment_effects()
	item_unequipped.emit(self)
	EventBus.item_unequipped.emit(self, get_equipment_slot())
	
	return true

func can_equip() -> bool:
	# Override in subclasses for specific equipment requirements
	return item_type in [
		GameEnums.ItemType.WEAPON,
		GameEnums.ItemType.ARMOR,
		GameEnums.ItemType.WINGS,
		GameEnums.ItemType.ACCESSORY
	]

func get_equipment_slot() -> String:
	# Override in subclasses to return specific slot name
	match item_type:
		GameEnums.ItemType.WEAPON:
			return "weapon"
		GameEnums.ItemType.ARMOR:
			return "armor"
		GameEnums.ItemType.WINGS:
			return "wings"
		GameEnums.ItemType.ACCESSORY:
			return "accessory"
		_:
			return "none"

func _apply_equipment_effects() -> void:
	# Apply stat modifiers and special effects
	EventBus.player_stats_changed.emit(get_total_stat_modifiers())

func _remove_equipment_effects() -> void:
	# Remove stat modifiers and special effects
	var negative_stats = {}
	for stat in stat_modifiers:
		negative_stats[stat] = -stat_modifiers[stat]
	EventBus.player_stats_changed.emit(negative_stats)

## Socket System
func add_socket() -> bool:
	if socket_count >= GameConstants.MAX_SOCKETS_PER_ITEM:
		return false
	
	socket_count += 1
	socketed_runes.append(null)
	return true

func socket_rune(socket_index: int, rune: Resource) -> bool:
	if socket_index < 0 or socket_index >= socket_count:
		return false
	
	if socketed_runes[socket_index] != null:
		return false  # Socket already occupied
	
	socketed_runes[socket_index] = rune
	socket_filled.emit(socket_index, rune)
	
	if is_equipped:
		_apply_equipment_effects()
	
	return true

func remove_rune(socket_index: int) -> Resource:
	if socket_index < 0 or socket_index >= socket_count:
		return null
	
	var removed_rune = socketed_runes[socket_index]
	socketed_runes[socket_index] = null
	socket_emptied.emit(socket_index)
	
	if is_equipped:
		_apply_equipment_effects()
	
	return removed_rune

func get_socketed_rune(socket_index: int) -> Resource:
	if socket_index < 0 or socket_index >= socket_count:
		return null
	return socketed_runes[socket_index]

func get_empty_socket_count() -> int:
	var empty_count = 0
	for rune in socketed_runes:
		if rune == null:
			empty_count += 1
	return empty_count

## Stat Calculation
func get_total_stat_modifiers() -> Dictionary:
	var total_stats = stat_modifiers.duplicate()
	
	# Add rune bonuses
	for rune in socketed_runes:
		if rune != null and rune.has_method("get_stat_bonuses"):
			var rune_stats = rune.get_stat_bonuses()
			for stat in rune_stats:
				total_stats[stat] = total_stats.get(stat, 0) + rune_stats[stat]
	
	return total_stats

func get_stat_value(stat_type: GameEnums.StatType) -> float:
	return get_total_stat_modifiers().get(stat_type, 0.0)

## Durability System
func damage_durability(amount: int) -> void:
	durability = max(0, durability - amount)
	durability_changed.emit(durability, max_durability)
	
	if durability <= 0:
		_handle_item_broken()

func repair_durability(amount: int) -> void:
	durability = min(max_durability, durability + amount)
	durability_changed.emit(durability, max_durability)

func get_durability_percentage() -> float:
	if max_durability <= 0:
		return 1.0
	return float(durability) / float(max_durability)

func _handle_item_broken() -> void:
	if is_equipped:
		unequip()
	EventBus.notification_shown.emit("%s is broken!" % name, "warning")

## Utility Functions
func get_display_name() -> String:
	var color = GameEnums.get_rarity_color(rarity)
	return "[color=%s]%s[/color]" % [color.to_html(), name]

func get_tooltip_text() -> String:
	var tooltip = "[b]%s[/b]\n" % get_display_name()
	tooltip += "[i]%s[/i]\n\n" % description
	
	# Add stats
	var total_stats = get_total_stat_modifiers()
	for stat in total_stats:
		var value = total_stats[stat]
		if value != 0:
			var stat_name = GameEnums.StatType.keys()[stat]
			var icon = GameEnums.get_stat_icon(stat)
			tooltip += "%s %s: +%s\n" % [icon, stat_name, value]
	
	# Add socket info
	if socket_count > 0:
		tooltip += "\nSockets: %d/%d filled" % [socket_count - get_empty_socket_count(), socket_count]
	
	# Add durability info
	if max_durability > 0:
		tooltip += "\nDurability: %d/%d" % [durability, max_durability]
	
	# Add level requirement
	if level_requirement > 1:
		tooltip += "\nLevel Required: %d" % level_requirement
	
	return tooltip

func can_stack_with(other_item: Item) -> bool:
	return (id == other_item.id and 
			rarity == other_item.rarity and
			current_stack < stack_size and
			other_item.current_stack < other_item.stack_size)

func stack_with(other_item: Item) -> int:
	if not can_stack_with(other_item):
		return 0
	
	var space_available = stack_size - current_stack
	var amount_to_transfer = min(space_available, other_item.current_stack)
	
	current_stack += amount_to_transfer
	other_item.current_stack -= amount_to_transfer
	
	return amount_to_transfer

func split_stack(amount: int) -> Item:
	if amount <= 0 or amount >= current_stack:
		return null
	
	var new_item = duplicate()
	new_item.current_stack = amount
	current_stack -= amount
	
	return new_item

func get_total_value() -> int:
	return sell_value * current_stack

## Serialization
func to_dict() -> Dictionary:
	return {
		"id": id,
		"name": name,
		"description": description,
		"item_type": item_type,
		"rarity": rarity,
		"level_requirement": level_requirement,
		"current_stack": current_stack,
		"durability": durability,
		"max_durability": max_durability,
		"stat_modifiers": stat_modifiers,
		"socketed_runes": _serialize_runes(),
		"is_equipped": is_equipped
	}

func from_dict(data: Dictionary) -> void:
	id = data.get("id", "")
	name = data.get("name", "")
	description = data.get("description", "")
	item_type = data.get("item_type", GameEnums.ItemType.CONSUMABLE)
	rarity = data.get("rarity", GameEnums.ItemRarity.COMMON)
	level_requirement = data.get("level_requirement", 1)
	current_stack = data.get("current_stack", 1)
	durability = data.get("durability", 100)
	max_durability = data.get("max_durability", 100)
	stat_modifiers = data.get("stat_modifiers", {})
	is_equipped = data.get("is_equipped", false)
	_deserialize_runes(data.get("socketed_runes", []))

func _serialize_runes() -> Array:
	var serialized = []
	for rune in socketed_runes:
		if rune != null and rune.has_method("to_dict"):
			serialized.append(rune.to_dict())
		else:
			serialized.append(null)
	return serialized

func _deserialize_runes(rune_data: Array) -> void:
	socketed_runes.clear()
	for data in rune_data:
		if data != null:
			# This would need to be implemented with a rune factory
			socketed_runes.append(null)  # Placeholder
		else:
			socketed_runes.append(null)
