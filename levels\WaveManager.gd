extends Node

## Wave management system implementing procedural level generation
## Handles wave progression, difficulty scaling, and special events

signal wave_preparation_started(wave_number: int, preparation_time: float)
signal wave_in_progress(wave_number: int)
signal wave_completed(wave_number: int, completion_time: float)
signal boss_wave_started(wave_number: int)
signal run_completed(success: bool, final_wave: int)

@export var preparation_time: float = 3.0
@export var max_waves_per_run: int = 20
@export var boss_wave_interval: int = 5

# Wave state
var current_wave: int = 0
var wave_start_time: float = 0.0
var is_wave_active: bool = false
var is_preparation_phase: bool = false
var preparation_timer: float = 0.0

# Wave configuration
var wave_configs: Dictionary = {}
var special_wave_events: Dictionary = {}

# References
var main_scene: Node3D
var enemy_spawner: EnemySpawner
var loot_manager: Node

func _ready() -> void:
	_setup_wave_configurations()
	_setup_special_events()
	_connect_events()

func initialize(scene: Node3D) -> void:
	main_scene = scene
	enemy_spawner = scene.get_node("GameSystems/EnemySpawner")
	loot_manager = scene.get_node("GameSystems/LootManager")

func _setup_wave_configurations() -> void:
	# Define wave configurations with scaling difficulty
	for wave in range(1, max_waves_per_run + 1):
		wave_configs[wave] = _generate_wave_config(wave)

func _generate_wave_config(wave_number: int) -> Dictionary:
	var config = {
		"wave_number": wave_number,
		"enemy_count": GameConstants.get_enemies_for_wave(wave_number),
		"enemy_level": wave_number,
		"is_boss_wave": GameConstants.is_boss_wave(wave_number),
		"special_modifiers": [],
		"environmental_effects": [],
		"loot_multiplier": 1.0,
		"experience_multiplier": 1.0
	}
	
	# Add special modifiers based on wave number
	if wave_number >= 10:
		config.special_modifiers.append("increased_enemy_speed")
	
	if wave_number >= 15:
		config.special_modifiers.append("enemy_regeneration")
	
	if wave_number >= 18:
		config.special_modifiers.append("double_enemies")
	
	# Boss wave modifications
	if config.is_boss_wave:
		config.enemy_count = 1  # Single boss
		config.loot_multiplier = 2.0
		config.experience_multiplier = 1.5
		config.special_modifiers.append("boss_aura")
	
	# Environmental effects
	match wave_number % 4:
		1:
			config.environmental_effects.append("fog")
		2:
			config.environmental_effects.append("rain")
		3:
			config.environmental_effects.append("wind")
		0:
			config.environmental_effects.append("clear")
	
	return config

func _setup_special_events() -> void:
	# Define special wave events
	special_wave_events = {
		5: "first_boss",
		10: "elite_swarm",
		15: "environmental_hazard",
		20: "final_boss"
	}

func _connect_events() -> void:
	EventBus.wave_started.connect(_on_wave_started)
	EventBus.wave_completed.connect(_on_wave_completed)
	if enemy_spawner:
		enemy_spawner.all_enemies_defeated.connect(_on_all_enemies_defeated)

func _process(delta: float) -> void:
	if is_preparation_phase:
		_update_preparation_phase(delta)

## Wave Control
func start_waves() -> void:
	current_wave = 0
	_start_next_wave()

func _start_next_wave() -> void:
	current_wave += 1
	
	if current_wave > max_waves_per_run:
		_complete_run(true)
		return
	
	_start_preparation_phase()

func _start_preparation_phase() -> void:
	is_preparation_phase = true
	preparation_timer = preparation_time
	
	var config = wave_configs[current_wave]
	wave_preparation_started.emit(current_wave, preparation_time)
	
	# Show wave info to player
	var wave_info = _get_wave_info_text(config)
	EventBus.notification_shown.emit(wave_info, "info")
	
	print("Preparing for wave %d..." % current_wave)

func _update_preparation_phase(delta: float) -> void:
	preparation_timer -= delta
	
	if preparation_timer <= 0:
		_start_wave_combat()

func _start_wave_combat() -> void:
	is_preparation_phase = false
	is_wave_active = true
	wave_start_time = Time.get_time_dict_from_system()["unix"]
	
	var config = wave_configs[current_wave]
	
	# Apply wave modifiers
	_apply_wave_modifiers(config)
	
	# Apply environmental effects
	_apply_environmental_effects(config)
	
	# Trigger special events
	if special_wave_events.has(current_wave):
		_trigger_special_event(special_wave_events[current_wave])
	
	# Start enemy spawning
	wave_in_progress.emit(current_wave)
	EventBus.wave_started.emit(current_wave)
	
	# Special handling for boss waves
	if config.is_boss_wave:
		boss_wave_started.emit(current_wave)
		EventBus.notification_shown.emit("BOSS WAVE!", "warning")
	
	print("Wave %d started!" % current_wave)

## Wave Completion
func _on_wave_started(wave_number: int) -> void:
	# Wave started by enemy spawner
	pass

func _on_wave_completed(wave_number: int) -> void:
	# Wave completed by enemy spawner
	pass

func _on_all_enemies_defeated() -> void:
	if not is_wave_active:
		return
	
	_complete_current_wave()

func _complete_current_wave() -> void:
	is_wave_active = false
	var completion_time = Time.get_time_dict_from_system()["unix"] - wave_start_time
	
	var config = wave_configs[current_wave]
	
	# Calculate wave rewards
	var rewards = _calculate_wave_rewards(config, completion_time)
	
	# Grant rewards to player
	_grant_wave_rewards(rewards)
	
	# Remove wave modifiers
	_remove_wave_modifiers(config)
	
	# Remove environmental effects
	_remove_environmental_effects(config)
	
	wave_completed.emit(current_wave, completion_time)
	
	print("Wave %d completed in %.1f seconds!" % [current_wave, completion_time])
	
	# Start next wave after delay
	get_tree().create_timer(2.0).timeout.connect(_start_next_wave)

func _complete_run(success: bool) -> void:
	run_completed.emit(success, current_wave)
	EventBus.run_completed.emit(success, _calculate_run_rewards())
	
	var message = "Run Completed!" if success else "Run Failed!"
	EventBus.notification_shown.emit(message, "achievement" if success else "error")

## Wave Modifiers
func _apply_wave_modifiers(config: Dictionary) -> void:
	for modifier in config.special_modifiers:
		match modifier:
			"increased_enemy_speed":
				_apply_speed_modifier(1.5)
			"enemy_regeneration":
				_apply_regeneration_modifier()
			"double_enemies":
				_apply_double_enemies_modifier()
			"boss_aura":
				_apply_boss_aura_modifier()

func _remove_wave_modifiers(config: Dictionary) -> void:
	for modifier in config.special_modifiers:
		match modifier:
			"increased_enemy_speed":
				_remove_speed_modifier()
			"enemy_regeneration":
				_remove_regeneration_modifier()
			"double_enemies":
				_remove_double_enemies_modifier()
			"boss_aura":
				_remove_boss_aura_modifier()

func _apply_speed_modifier(multiplier: float) -> void:
	EventBus.emit_signal("global_modifier_applied", "enemy_speed", multiplier)

func _remove_speed_modifier() -> void:
	EventBus.emit_signal("global_modifier_removed", "enemy_speed")

func _apply_regeneration_modifier() -> void:
	EventBus.emit_signal("global_modifier_applied", "enemy_regeneration", true)

func _remove_regeneration_modifier() -> void:
	EventBus.emit_signal("global_modifier_removed", "enemy_regeneration")

func _apply_double_enemies_modifier() -> void:
	if enemy_spawner:
		enemy_spawner.max_enemies_alive *= 2

func _remove_double_enemies_modifier() -> void:
	if enemy_spawner:
		enemy_spawner.max_enemies_alive /= 2

func _apply_boss_aura_modifier() -> void:
	EventBus.emit_signal("boss_aura_activated")

func _remove_boss_aura_modifier() -> void:
	EventBus.emit_signal("boss_aura_deactivated")

## Environmental Effects
func _apply_environmental_effects(config: Dictionary) -> void:
	for effect in config.environmental_effects:
		match effect:
			"fog":
				_apply_fog_effect()
			"rain":
				_apply_rain_effect()
			"wind":
				_apply_wind_effect()
			"clear":
				_apply_clear_effect()

func _remove_environmental_effects(config: Dictionary) -> void:
	for effect in config.environmental_effects:
		match effect:
			"fog":
				_remove_fog_effect()
			"rain":
				_remove_rain_effect()
			"wind":
				_remove_wind_effect()
			"clear":
				_remove_clear_effect()

func _apply_fog_effect() -> void:
	EventBus.effect_requested.emit("fog", Vector3.ZERO)
	# Reduce visibility, increase stealth

func _remove_fog_effect() -> void:
	EventBus.effect_requested.emit("clear_fog", Vector3.ZERO)

func _apply_rain_effect() -> void:
	EventBus.effect_requested.emit("rain", Vector3.ZERO)
	# Reduce fire damage, increase lightning damage

func _remove_rain_effect() -> void:
	EventBus.effect_requested.emit("clear_rain", Vector3.ZERO)

func _apply_wind_effect() -> void:
	EventBus.effect_requested.emit("wind", Vector3.ZERO)
	# Affect projectiles, increase movement speed

func _remove_wind_effect() -> void:
	EventBus.effect_requested.emit("clear_wind", Vector3.ZERO)

func _apply_clear_effect() -> void:
	# No special effects, clear weather
	pass

func _remove_clear_effect() -> void:
	# Nothing to remove
	pass

## Special Events
func _trigger_special_event(event_name: String) -> void:
	match event_name:
		"first_boss":
			_trigger_first_boss_event()
		"elite_swarm":
			_trigger_elite_swarm_event()
		"environmental_hazard":
			_trigger_environmental_hazard_event()
		"final_boss":
			_trigger_final_boss_event()

func _trigger_first_boss_event() -> void:
	EventBus.notification_shown.emit("The first boss approaches!", "warning")
	EventBus.music_changed.emit("boss_fight")

func _trigger_elite_swarm_event() -> void:
	EventBus.notification_shown.emit("Elite enemies swarm the battlefield!", "warning")
	# Spawn additional elite enemies

func _trigger_environmental_hazard_event() -> void:
	EventBus.notification_shown.emit("Environmental hazards activated!", "warning")
	# Activate traps and hazards

func _trigger_final_boss_event() -> void:
	EventBus.notification_shown.emit("The final boss emerges!", "warning")
	EventBus.music_changed.emit("final_boss")

## Rewards System
func _calculate_wave_rewards(config: Dictionary, completion_time: float) -> Dictionary:
	var base_gold = GameConstants.get_gold_reward_for_wave(current_wave)
	var base_experience = current_wave * 25
	
	# Apply multipliers
	var gold_reward = int(base_gold * config.loot_multiplier)
	var experience_reward = int(base_experience * config.experience_multiplier)
	
	# Time bonus (faster completion = more rewards)
	var time_bonus_multiplier = max(0.5, 2.0 - (completion_time / 60.0))
	gold_reward = int(gold_reward * time_bonus_multiplier)
	
	return {
		"gold": gold_reward,
		"experience": experience_reward,
		"items": _calculate_item_rewards(config)
	}

func _calculate_item_rewards(config: Dictionary) -> Array:
	var items = []
	
	# Boss waves guarantee item drops
	if config.is_boss_wave:
		items.append({
			"rarity": GameEnums.ItemRarity.EPIC,
			"level": current_wave
		})
	
	# Random item chance for regular waves
	elif randf() < 0.3:  # 30% chance
		var rarity = GameEnums.ItemRarity.COMMON
		if current_wave >= 10:
			rarity = GameEnums.ItemRarity.UNCOMMON
		if current_wave >= 15:
			rarity = GameEnums.ItemRarity.RARE
		
		items.append({
			"rarity": rarity,
			"level": current_wave
		})
	
	return items

func _grant_wave_rewards(rewards: Dictionary) -> void:
	# Grant gold
	EventBus.currency_changed.emit("gold", rewards.gold)
	
	# Grant experience
	EventBus.experience_gained.emit(rewards.experience)
	
	# Grant items
	for item_data in rewards.items:
		if loot_manager and loot_manager.has_method("spawn_reward_item"):
			loot_manager.spawn_reward_item(item_data.rarity, item_data.level)

func _calculate_run_rewards() -> Array:
	var rewards = []
	
	# Base rewards for completing waves
	var gold_reward = current_wave * 50
	var experience_reward = current_wave * 100
	
	rewards.append({"type": "gold", "amount": gold_reward})
	rewards.append({"type": "experience", "amount": experience_reward})
	
	# Completion bonus
	if current_wave >= max_waves_per_run:
		rewards.append({"type": "gems", "amount": 10})
		rewards.append({"type": "achievement", "id": "run_completed"})
	
	return rewards

## Utility Functions
func get_current_wave() -> int:
	return current_wave

func get_wave_progress() -> float:
	return float(current_wave) / float(max_waves_per_run)

func is_boss_wave() -> bool:
	return wave_configs.get(current_wave, {}).get("is_boss_wave", false)

func get_waves_remaining() -> int:
	return max(0, max_waves_per_run - current_wave)

func _get_wave_info_text(config: Dictionary) -> String:
	var info = "Wave %d" % config.wave_number
	
	if config.is_boss_wave:
		info += " - BOSS WAVE"
	else:
		info += " - %d enemies" % config.enemy_count
	
	if not config.special_modifiers.is_empty():
		info += " (Special: %s)" % config.special_modifiers[0].replace("_", " ").capitalize()
	
	return info

## Debug Functions
func debug_skip_to_wave(wave_number: int) -> void:
	current_wave = wave_number - 1
	_start_next_wave()

func debug_complete_current_wave() -> void:
	if is_wave_active:
		_complete_current_wave()

func debug_print_wave_state() -> void:
	print("=== Wave Manager Debug ===")
	print("Current Wave: %d/%d" % [current_wave, max_waves_per_run])
	print("Wave Active: %s" % is_wave_active)
	print("Preparation Phase: %s" % is_preparation_phase)
	if is_preparation_phase:
		print("Preparation Time Remaining: %.1f" % preparation_timer)
	print("==========================")
