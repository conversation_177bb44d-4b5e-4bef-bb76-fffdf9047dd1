class_name LootPickup
extends Node3D

## Individual loot pickup implementing collection mechanics and visual effects
## Handles different loot types with appropriate behaviors

signal pickup_collected(pickup: LootPickup, collector: Node)
signal pickup_expired(pickup: LootPickup)

@export var pickup_type: String = "gold"  # gold, item, currency
@export var lifetime: float = 30.0
@export var bounce_amplitude: float = 0.5
@export var bounce_frequency: float = 2.0
@export var rotation_speed: float = 90.0

# Pickup data
var item_data: Item = null
var gold_amount: int = 0
var currency_type: String = ""
var currency_amount: int = 0

# State
var age: float = 0.0
var is_collected: bool = false
var can_attract: bool = true
var initial_position: Vector3
var bounce_offset: float = 0.0

# Components
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D
@onready var pickup_area: Area3D = $PickupArea
@onready var rigid_body: RigidBody3D = $RigidBody3D

func _ready() -> void:
	initial_position = global_position
	bounce_offset = randf() * TAU  # Random bounce phase
	_setup_pickup_area()

func _setup_pickup_area() -> void:
	if pickup_area:
		pickup_area.body_entered.connect(_on_pickup_area_entered)
		pickup_area.area_entered.connect(_on_pickup_area_entered)

func _process(delta: float) -> void:
	if is_collected:
		return
	
	update_pickup(delta)

func update_pickup(delta: float) -> void:
	age += delta
	
	# Update visual effects
	_update_bounce_animation(delta)
	_update_rotation(delta)
	_update_lifetime_effects()
	
	# Check for expiration
	if age >= lifetime:
		_expire_pickup()

## Setup Functions
func setup_gold(amount: int) -> void:
	pickup_type = "gold"
	gold_amount = amount
	_setup_gold_visual()

func setup_item(item: Item) -> void:
	pickup_type = "item"
	item_data = item
	_setup_item_visual()

func setup_currency(type: String, amount: int) -> void:
	pickup_type = "currency"
	currency_type = type
	currency_amount = amount
	_setup_currency_visual()

func _setup_gold_visual() -> void:
	if mesh_instance:
		var mesh = SphereMesh.new()
		mesh.radius = 0.2 + (gold_amount * 0.01)  # Scale with amount
		mesh_instance.mesh = mesh
		
		var material = StandardMaterial3D.new()
		material.albedo_color = Color.GOLD
		material.metallic = 0.8
		material.roughness = 0.2
		mesh_instance.material_override = material

func _setup_item_visual() -> void:
	if not item_data or not mesh_instance:
		return
	
	# Use different shapes for different item types
	var mesh: Mesh
	match item_data.item_type:
		GameEnums.ItemType.WEAPON:
			mesh = BoxMesh.new()
			mesh.size = Vector3(0.1, 0.6, 0.1)
		GameEnums.ItemType.ARMOR:
			mesh = BoxMesh.new()
			mesh.size = Vector3(0.4, 0.5, 0.2)
		GameEnums.ItemType.WINGS:
			mesh = PlaneMesh.new()
			mesh.size = Vector2(0.6, 0.4)
		_:
			mesh = CapsuleMesh.new()
			mesh.radius = 0.2
			mesh.height = 0.4
	
	mesh_instance.mesh = mesh
	
	# Color by rarity
	var material = StandardMaterial3D.new()
	material.albedo_color = GameEnums.get_rarity_color(item_data.rarity)
	material.emission = material.albedo_color * 0.3
	mesh_instance.material_override = material

func _setup_currency_visual() -> void:
	if mesh_instance:
		var mesh = CylinderMesh.new()
		mesh.top_radius = 0.15
		mesh.bottom_radius = 0.15
		mesh.height = 0.05
		mesh_instance.mesh = mesh
		
		var material = StandardMaterial3D.new()
		match currency_type:
			"gems":
				material.albedo_color = Color.CYAN
				material.emission = Color.CYAN * 0.5
			"rune_powder":
				material.albedo_color = Color.PURPLE
				material.emission = Color.PURPLE * 0.3
			_:
				material.albedo_color = Color.WHITE
		
		material.metallic = 0.6
		material.roughness = 0.3
		mesh_instance.material_override = material

## Visual Effects
func _update_bounce_animation(delta: float) -> void:
	var bounce_y = sin((age * bounce_frequency + bounce_offset) * TAU) * bounce_amplitude
	global_position.y = initial_position.y + bounce_y

func _update_rotation(delta: float) -> void:
	rotation.y += deg_to_rad(rotation_speed) * delta

func _update_lifetime_effects() -> void:
	# Flash when about to expire
	var time_remaining = lifetime - age
	if time_remaining < 5.0:
		var flash_intensity = 1.0 - (time_remaining / 5.0)
		var flash_alpha = 0.5 + sin(age * 10.0) * 0.5 * flash_intensity
		
		if mesh_instance and mesh_instance.material_override:
			mesh_instance.material_override.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
			mesh_instance.material_override.albedo_color.a = flash_alpha

## Collection System
func collect_pickup(collector: Node) -> void:
	if is_collected:
		return
	
	is_collected = true
	
	# Visual collection effect
	_play_collection_effect()
	
	# Emit collection signal
	pickup_collected.emit(self, collector)

func _play_collection_effect() -> void:
	# Create collection tween
	var tween = create_tween()
	tween.parallel().tween_property(self, "scale", Vector3.ZERO, 0.3)
	tween.parallel().tween_property(self, "global_position:y", global_position.y + 1.0, 0.3)
	
	# Play sound effect
	EventBus.sound_requested.emit("loot_pickup", global_position)
	
	# Particle effect
	EventBus.effect_requested.emit("loot_collected", global_position)

func _expire_pickup() -> void:
	if is_collected:
		return
	
	# Visual expiration effect
	var tween = create_tween()
	tween.tween_property(self, "modulate:a", 0.0, 1.0)
	tween.tween_callback(func(): pickup_expired.emit(self))

## Pickup Detection
func _on_pickup_area_entered(body: Node3D) -> void:
	if is_collected:
		return
	
	# Check if it's the player
	if body.is_in_group("player"):
		collect_pickup(body)

## Magnetic Attraction
func can_be_attracted() -> bool:
	return can_attract and not is_collected and age > 0.5  # Small delay before attraction

func set_attraction_enabled(enabled: bool) -> void:
	can_attract = enabled

## Data Access
func get_item() -> Item:
	return item_data

func get_amount() -> int:
	match pickup_type:
		"gold":
			return gold_amount
		"currency":
			return currency_amount
		_:
			return 0

func get_currency_type() -> String:
	return currency_type

func get_pickup_value() -> int:
	match pickup_type:
		"gold":
			return gold_amount
		"currency":
			return currency_amount * _get_currency_value_multiplier()
		"item":
			return item_data.sell_value if item_data else 0
		_:
			return 0

func _get_currency_value_multiplier() -> int:
	match currency_type:
		"gems":
			return 10  # Gems are worth 10x gold
		"rune_powder":
			return 5   # Rune powder is worth 5x gold
		_:
			return 1

## Special Effects
func add_glow_effect() -> void:
	if mesh_instance and mesh_instance.material_override:
		mesh_instance.material_override.emission_enabled = true
		mesh_instance.material_override.emission = mesh_instance.material_override.albedo_color * 0.5

func add_sparkle_effect() -> void:
	# Request sparkle particle effect
	EventBus.effect_requested.emit("loot_sparkle", global_position)

func make_magnetic() -> void:
	can_attract = true
	add_glow_effect()

func make_valuable() -> void:
	# Increase visual prominence for valuable items
	scale *= 1.2
	bounce_amplitude *= 1.5
	rotation_speed *= 1.5
	add_sparkle_effect()

## Utility Functions
func get_display_text() -> String:
	match pickup_type:
		"gold":
			return "+%d Gold" % gold_amount
		"currency":
			return "+%d %s" % [currency_amount, currency_type.capitalize()]
		"item":
			return item_data.name if item_data else "Unknown Item"
		_:
			return "Pickup"

func get_rarity_color() -> Color:
	match pickup_type:
		"item":
			return GameEnums.get_rarity_color(item_data.rarity) if item_data else Color.WHITE
		"gold":
			return Color.GOLD
		"currency":
			match currency_type:
				"gems":
					return Color.CYAN
				"rune_powder":
					return Color.PURPLE
				_:
					return Color.WHITE
		_:
			return Color.WHITE

func is_rare_loot() -> bool:
	match pickup_type:
		"item":
			return item_data.rarity >= GameEnums.ItemRarity.RARE if item_data else false
		"currency":
			return currency_amount >= 10
		"gold":
			return gold_amount >= 50
		_:
			return false

## Serialization (for save/load if needed)
func to_dict() -> Dictionary:
	var data = {
		"pickup_type": pickup_type,
		"position": global_position,
		"age": age
	}
	
	match pickup_type:
		"gold":
			data["gold_amount"] = gold_amount
		"currency":
			data["currency_type"] = currency_type
			data["currency_amount"] = currency_amount
		"item":
			data["item_data"] = item_data.to_dict() if item_data else {}
	
	return data

func from_dict(data: Dictionary) -> void:
	pickup_type = data.get("pickup_type", "gold")
	global_position = data.get("position", Vector3.ZERO)
	age = data.get("age", 0.0)
	
	match pickup_type:
		"gold":
			setup_gold(data.get("gold_amount", 0))
		"currency":
			setup_currency(data.get("currency_type", ""), data.get("currency_amount", 0))
		"item":
			var item_dict = data.get("item_data", {})
			if not item_dict.is_empty():
				var item = Item.new()
				item.from_dict(item_dict)
				setup_item(item)

## Debug Functions
func debug_print_pickup_info() -> void:
	print("=== Pickup Debug Info ===")
	print("Type: %s" % pickup_type)
	print("Age: %.1f/%.1f" % [age, lifetime])
	print("Position: %s" % global_position)
	print("Collected: %s" % is_collected)
	print("Can Attract: %s" % can_attract)
	match pickup_type:
		"gold":
			print("Gold Amount: %d" % gold_amount)
		"currency":
			print("Currency: %d %s" % [currency_amount, currency_type])
		"item":
			print("Item: %s" % (item_data.name if item_data else "None"))
	print("========================")
