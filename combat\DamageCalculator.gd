class_name DamageCalculator
extends RefCounted

## Damage calculation system implementing the Strategy pattern
## Handles all damage types, modifiers, and special effects

# Damage calculation results
class DamageResult:
	var base_damage: int = 0
	var final_damage: int = 0
	var damage_type: GameEnums.DamageType = GameEnums.DamageType.PHYSICAL
	var attack_result: GameEnums.AttackResult = GameEnums.AttackResult.HIT
	var modifiers_applied: Array[String] = []
	var effects_triggered: Array[String] = []
	var is_critical: bool = false
	var is_excellent: bool = false
	var is_double: bool = false
	var armor_reduction: int = 0
	var resistance_reduction: int = 0

static var rng: RandomNumberGenerator = RandomNumberGenerator.new()

static func _static_init() -> void:
	rng.randomize()

## Main Damage Calculation
static func calculate_damage(
	attacker_stats: Dictionary,
	target_stats: Dictionary,
	weapon: Weapon = null,
	skill_modifiers: Dictionary = {}
) -> DamageResult:
	
	var result = DamageResult.new()
	
	# Step 1: Calculate base damage
	result.base_damage = _calculate_base_damage(attacker_stats, weapon)
	result.final_damage = result.base_damage
	
	# Step 2: Apply damage variance
	result.final_damage = _apply_damage_variance(result.final_damage)
	
	# Step 3: Determine attack result (hit, crit, miss, etc.)
	result.attack_result = _determine_attack_result(attacker_stats, target_stats)
	
	# Step 4: Apply attack result modifiers
	result = _apply_attack_result_modifiers(result, attacker_stats)
	
	# Step 5: Apply skill modifiers
	result = _apply_skill_modifiers(result, skill_modifiers)
	
	# Step 6: Apply weapon special effects
	if weapon:
		result = _apply_weapon_effects(result, weapon, attacker_stats)
	
	# Step 7: Apply target defenses
	result = _apply_target_defenses(result, target_stats)
	
	# Step 8: Ensure minimum damage
	result.final_damage = max(1, result.final_damage)
	
	return result

## Base Damage Calculation
static func _calculate_base_damage(attacker_stats: Dictionary, weapon: Weapon) -> int:
	var base_attack = attacker_stats.get(GameEnums.StatType.ATTACK, 10)
	var weapon_damage = 0
	
	if weapon:
		weapon_damage = weapon.base_damage
		weapon_damage += weapon.get_stat_value(GameEnums.StatType.ATTACK)
	
	return base_attack + weapon_damage

static func _apply_damage_variance(damage: int) -> int:
	var variance = GameConstants.DAMAGE_VARIANCE
	var min_damage = damage * (1.0 - variance)
	var max_damage = damage * (1.0 + variance)
	return int(rng.randf_range(min_damage, max_damage))

## Attack Result Determination
static func _determine_attack_result(
	attacker_stats: Dictionary, 
	target_stats: Dictionary
) -> GameEnums.AttackResult:
	
	# Check for miss
	var accuracy = attacker_stats.get("accuracy", 1.0)
	var evasion = target_stats.get("evasion", 0.0)
	var hit_chance = accuracy - evasion
	
	if rng.randf() > hit_chance:
		return GameEnums.AttackResult.MISS
	
	# Check for critical hit
	var crit_chance = attacker_stats.get(GameEnums.StatType.CRIT_CHANCE, 0.05)
	if rng.randf() < crit_chance:
		# Check for excellent critical
		if rng.randf() < 0.1:  # 10% chance for excellent on crit
			return GameEnums.AttackResult.EXCELLENT
		return GameEnums.AttackResult.CRITICAL
	
	# Check for double hit
	if rng.randf() < GameConstants.DOUBLE_HIT_CHANCE:
		return GameEnums.AttackResult.DOUBLE
	
	return GameEnums.AttackResult.HIT

## Attack Result Modifiers
static func _apply_attack_result_modifiers(
	result: DamageResult, 
	attacker_stats: Dictionary
) -> DamageResult:
	
	match result.attack_result:
		GameEnums.AttackResult.MISS:
			result.final_damage = 0
			result.modifiers_applied.append("miss")
		
		GameEnums.AttackResult.CRITICAL:
			var crit_damage = attacker_stats.get(GameEnums.StatType.CRIT_DAMAGE, 1.5)
			result.final_damage = int(result.final_damage * crit_damage)
			result.is_critical = true
			result.modifiers_applied.append("critical")
		
		GameEnums.AttackResult.EXCELLENT:
			var excellent_multiplier = GameConstants.EXCELLENT_HIT_MULTIPLIER
			var crit_damage = attacker_stats.get(GameEnums.StatType.CRIT_DAMAGE, 1.5)
			result.final_damage = int(result.final_damage * excellent_multiplier * crit_damage)
			result.is_critical = true
			result.is_excellent = true
			result.modifiers_applied.append("excellent")
		
		GameEnums.AttackResult.DOUBLE:
			result.final_damage *= 2
			result.is_double = true
			result.modifiers_applied.append("double")
		
		GameEnums.AttackResult.BLOCKED:
			result.final_damage = int(result.final_damage * 0.5)
			result.modifiers_applied.append("blocked")
		
		GameEnums.AttackResult.DODGED:
			result.final_damage = 0
			result.modifiers_applied.append("dodged")
	
	return result

## Skill Modifiers
static func _apply_skill_modifiers(
	result: DamageResult, 
	skill_modifiers: Dictionary
) -> DamageResult:
	
	for modifier_type in skill_modifiers:
		var value = skill_modifiers[modifier_type]
		
		match modifier_type:
			"damage_multiplier":
				result.final_damage = int(result.final_damage * value)
				result.modifiers_applied.append("skill_multiplier")
			
			"flat_damage_bonus":
				result.final_damage += int(value)
				result.modifiers_applied.append("flat_bonus")
			
			"crit_chance_bonus":
				if not result.is_critical and rng.randf() < value:
					result.final_damage = int(result.final_damage * 2.0)
					result.is_critical = true
					result.modifiers_applied.append("skill_crit")
			
			"damage_type_override":
				result.damage_type = value
				result.modifiers_applied.append("type_override")
	
	return result

## Weapon Effects
static func _apply_weapon_effects(
	result: DamageResult, 
	weapon: Weapon, 
	attacker_stats: Dictionary
) -> DamageResult:
	
	# Apply weapon-specific passive effects
	for effect in weapon.passive_effects:
		match effect:
			"spell_power":
				if result.damage_type == GameEnums.DamageType.MAGICAL:
					var bonus = weapon.passive_effects[effect]
					result.final_damage = int(result.final_damage * (1.0 + bonus))
					result.modifiers_applied.append("spell_power")
			
			"backstab_damage":
				# This would need position checking in actual implementation
				var bonus = weapon.passive_effects[effect]
				result.final_damage = int(result.final_damage * bonus)
				result.modifiers_applied.append("backstab")
			
			"area_damage":
				result.effects_triggered.append("area_damage")
			
			"armor_penetration":
				result.effects_triggered.append("armor_penetration")
			
			"piercing_shot":
				result.effects_triggered.append("piercing")
	
	# Apply weapon type bonuses
	match weapon.weapon_type:
		GameEnums.WeaponType.DAGGER:
			# Daggers have higher crit chance
			if weapon.critical_bonus > 0 and not result.is_critical:
				if rng.randf() < weapon.critical_bonus:
					result.final_damage = int(result.final_damage * 2.0)
					result.is_critical = true
					result.modifiers_applied.append("weapon_crit")
	
	return result

## Target Defenses
static func _apply_target_defenses(
	result: DamageResult, 
	target_stats: Dictionary
) -> DamageResult:
	
	var defense = target_stats.get(GameEnums.StatType.DEFENSE, 0)
	var magic_resistance = target_stats.get("magic_resistance", 0.0)
	
	# Apply armor reduction for physical damage
	if result.damage_type == GameEnums.DamageType.PHYSICAL:
		var armor_reduction = min(result.final_damage * 0.8, defense * 0.5)
		result.armor_reduction = int(armor_reduction)
		result.final_damage -= result.armor_reduction
		result.modifiers_applied.append("armor_reduction")
	
	# Apply magic resistance for magical damage
	elif result.damage_type == GameEnums.DamageType.MAGICAL:
		var resistance_reduction = int(result.final_damage * magic_resistance)
		result.resistance_reduction = resistance_reduction
		result.final_damage -= resistance_reduction
		result.modifiers_applied.append("magic_resistance")
	
	# True damage ignores all defenses
	elif result.damage_type == GameEnums.DamageType.TRUE:
		result.modifiers_applied.append("true_damage")
	
	return result

## Specialized Damage Calculations
static func calculate_spell_damage(
	caster_stats: Dictionary,
	spell_power: int,
	target_stats: Dictionary,
	spell_modifiers: Dictionary = {}
) -> DamageResult:
	
	var attacker_stats = caster_stats.duplicate()
	attacker_stats[GameEnums.StatType.ATTACK] = spell_power
	
	var skill_mods = spell_modifiers.duplicate()
	skill_mods["damage_type_override"] = GameEnums.DamageType.MAGICAL
	
	return calculate_damage(attacker_stats, target_stats, null, skill_mods)

static func calculate_dot_damage(
	base_damage: int,
	duration: float,
	tick_rate: float = 1.0
) -> Array[int]:
	
	var ticks = int(duration / tick_rate)
	var damage_per_tick = base_damage / ticks
	var damage_array = []
	
	for i in ticks:
		damage_array.append(int(damage_per_tick))
	
	return damage_array

static func calculate_area_damage(
	center_damage: int,
	distance_from_center: float,
	max_range: float,
	falloff_type: String = "linear"
) -> int:
	
	if distance_from_center >= max_range:
		return 0
	
	var damage_multiplier = 1.0
	
	match falloff_type:
		"linear":
			damage_multiplier = 1.0 - (distance_from_center / max_range)
		"quadratic":
			var normalized_distance = distance_from_center / max_range
			damage_multiplier = 1.0 - (normalized_distance * normalized_distance)
		"none":
			damage_multiplier = 1.0
	
	return int(center_damage * damage_multiplier)

## Utility Functions
static func get_damage_color(result: DamageResult) -> Color:
	if result.attack_result == GameEnums.AttackResult.MISS:
		return Color.GRAY
	
	if result.is_excellent:
		return Color.PURPLE
	elif result.is_critical:
		return Color.YELLOW
	elif result.is_double:
		return Color.ORANGE
	
	match result.damage_type:
		GameEnums.DamageType.PHYSICAL:
			return Color.WHITE
		GameEnums.DamageType.MAGICAL:
			return Color.CYAN
		GameEnums.DamageType.TRUE:
			return Color.PURPLE
		_:
			return Color.WHITE

static func get_damage_text(result: DamageResult) -> String:
	if result.attack_result == GameEnums.AttackResult.MISS:
		return "MISS"
	
	var text = str(result.final_damage)
	
	if result.is_excellent:
		text = "EXCELLENT! " + text
	elif result.is_critical:
		text = "CRIT! " + text
	elif result.is_double:
		text = "DOUBLE! " + text
	
	return text

static func should_trigger_effect(effect_name: String, result: DamageResult) -> bool:
	return effect_name in result.effects_triggered
