class_name PlayerInput
extends Node

## Mobile-optimized input handler for player controls
## Supports virtual joystick, touch gestures, and keyboard fallback

signal movement_input(direction: Vector2)
signal skill_activated(skill_id: String)
signal touch_gesture(gesture_type: String, position: Vector2)

# Virtual joystick settings
@export var joystick_deadzone: float = 0.2
@export var joystick_sensitivity: float = 1.0
@export var touch_anywhere_joystick: bool = true

# Input state
var virtual_joystick_active: bool = false
var joystick_center: Vector2 = Vector2.ZERO
var joystick_touch_index: int = -1
var current_movement_direction: Vector2 = Vector2.ZERO

# Touch tracking
var active_touches: Dictionary = {}
var skill_button_positions: Dictionary = {}

# Gesture detection
var gesture_start_position: Vector2 = Vector2.ZERO
var gesture_start_time: float = 0.0
var min_swipe_distance: float = 100.0
var max_tap_duration: float = 0.3

func _ready() -> void:
	_setup_skill_buttons()
	_setup_input_handling()

func _setup_skill_buttons() -> void:
	# Define skill button areas (relative to screen size)
	var screen_size = get_viewport().get_visible_rect().size
	
	skill_button_positions = {
		"skill_1": Rect2(screen_size.x - 120, screen_size.y - 120, 80, 80),
		"skill_2": Rect2(screen_size.x - 220, screen_size.y - 120, 80, 80),
		"dash": Rect2(screen_size.x - 120, screen_size.y - 220, 80, 80),
		"flight": Rect2(screen_size.x - 220, screen_size.y - 220, 80, 80)
	}

func _setup_input_handling() -> void:
	# Enable input processing
	set_process_input(true)
	set_process_unhandled_input(true)

func _input(event: InputEvent) -> void:
	if event is InputEventScreenTouch:
		_handle_touch_input(event)
	elif event is InputEventScreenDrag:
		_handle_drag_input(event)
	elif event is InputEventKey:
		_handle_keyboard_input(event)

func _unhandled_input(event: InputEvent) -> void:
	# Handle any unprocessed input events
	pass

## Touch Input Handling
func _handle_touch_input(event: InputEventScreenTouch) -> void:
	var touch_pos = event.position
	var touch_index = event.index
	
	if event.pressed:
		_on_touch_start(touch_pos, touch_index)
	else:
		_on_touch_end(touch_pos, touch_index)

func _on_touch_start(position: Vector2, touch_index: int) -> void:
	active_touches[touch_index] = {
		"start_position": position,
		"current_position": position,
		"start_time": Time.get_unix_time_from_system(),
		"type": "unknown"
	}
	
	# Check if touch is on a skill button
	var skill_pressed = _check_skill_button_press(position)
	if skill_pressed:
		active_touches[touch_index].type = "skill"
		return
	
	# Check if this should be the movement joystick
	if not virtual_joystick_active and (touch_anywhere_joystick or _is_in_joystick_area(position)):
		_start_virtual_joystick(position, touch_index)
		active_touches[touch_index].type = "joystick"

func _on_touch_end(position: Vector2, touch_index: int) -> void:
	if not active_touches.has(touch_index):
		return
	
	var touch_data = active_touches[touch_index]
	var duration = Time.get_unix_time_from_system() - touch_data.start_time
	
	match touch_data.type:
		"joystick":
			_end_virtual_joystick(touch_index)
		"skill":
			_handle_skill_button_release(position)
		"unknown":
			_handle_gesture_end(touch_data, duration)
	
	active_touches.erase(touch_index)

func _handle_drag_input(event: InputEventScreenDrag) -> void:
	var touch_index = event.index
	
	if not active_touches.has(touch_index):
		return
	
	active_touches[touch_index].current_position = event.position
	
	if touch_index == joystick_touch_index:
		_update_virtual_joystick(event.position)

## Virtual Joystick System
func _start_virtual_joystick(position: Vector2, touch_index: int) -> void:
	virtual_joystick_active = true
	joystick_center = position
	joystick_touch_index = touch_index
	
	# Visual feedback for joystick start
	EventBus.emit_signal("virtual_joystick_started", position)

func _update_virtual_joystick(current_position: Vector2) -> void:
	if not virtual_joystick_active:
		return
	
	var offset = current_position - joystick_center
	var distance = offset.length()
	
	# Apply deadzone
	if distance < joystick_deadzone * GameConstants.VIRTUAL_JOYSTICK_SIZE:
		current_movement_direction = Vector2.ZERO
	else:
		# Normalize and apply sensitivity
		var max_distance = GameConstants.VIRTUAL_JOYSTICK_SIZE
		var normalized_offset = offset / max_distance
		normalized_offset = normalized_offset.limit_length(1.0)
		current_movement_direction = normalized_offset * joystick_sensitivity
	
	# Emit movement input
	movement_input.emit(current_movement_direction)
	
	# Visual feedback
	EventBus.emit_signal("virtual_joystick_moved", current_movement_direction)

func _end_virtual_joystick(touch_index: int) -> void:
	if touch_index != joystick_touch_index:
		return
	
	virtual_joystick_active = false
	joystick_touch_index = -1
	current_movement_direction = Vector2.ZERO
	
	# Stop movement
	movement_input.emit(Vector2.ZERO)
	
	# Visual feedback
	EventBus.emit_signal("virtual_joystick_ended")

func _is_in_joystick_area(position: Vector2) -> bool:
	# Left half of screen for joystick
	var screen_size = get_viewport().get_visible_rect().size
	return position.x < screen_size.x * 0.5

## Skill Button Handling
func _check_skill_button_press(position: Vector2) -> bool:
	for skill_id in skill_button_positions:
		var button_rect = skill_button_positions[skill_id]
		if button_rect.has_point(position):
			_activate_skill(skill_id)
			return true
	return false

func _handle_skill_button_release(position: Vector2) -> void:
	# Handle skill button release if needed
	pass

func _activate_skill(skill_id: String) -> void:
	skill_activated.emit(skill_id)
	
	# Visual feedback
	EventBus.effect_requested.emit("button_press", Vector3.ZERO)
	
	# Haptic feedback on mobile
	if OS.has_feature("mobile"):
		Input.vibrate_handheld(50)  # 50ms vibration

## Gesture Recognition
func _handle_gesture_end(touch_data: Dictionary, duration: float) -> void:
	var start_pos = touch_data.start_position
	var end_pos = touch_data.current_position
	var distance = start_pos.distance_to(end_pos)
	
	if duration < max_tap_duration and distance < 50:
		_handle_tap_gesture(end_pos)
	elif distance > min_swipe_distance:
		_handle_swipe_gesture(start_pos, end_pos)

func _handle_tap_gesture(position: Vector2) -> void:
	touch_gesture.emit("tap", position)
	
	# Could be used for target selection or interaction
	_check_world_interaction(position)

func _handle_swipe_gesture(start_pos: Vector2, end_pos: Vector2) -> void:
	var swipe_direction = (end_pos - start_pos).normalized()
	var swipe_angle = swipe_direction.angle()
	
	var gesture_type = ""
	if abs(swipe_angle) < PI/4:  # Right
		gesture_type = "swipe_right"
	elif abs(swipe_angle) > 3*PI/4:  # Left
		gesture_type = "swipe_left"
	elif swipe_angle > 0:  # Down
		gesture_type = "swipe_down"
	else:  # Up
		gesture_type = "swipe_up"
	
	touch_gesture.emit(gesture_type, start_pos)
	
	# Map swipes to actions
	match gesture_type:
		"swipe_up":
			skill_activated.emit("dash")
		"swipe_down":
			skill_activated.emit("flight")

func _check_world_interaction(screen_position: Vector2) -> void:
	# Convert screen position to world position for interaction
	var camera = get_viewport().get_camera_3d()
	if camera:
		var from = camera.project_ray_origin(screen_position)
		var to = from + camera.project_ray_normal(screen_position) * 100

		var space_state = camera.get_world_3d().direct_space_state
		var query = PhysicsRayQueryParameters3D.create(from, to)
		query.collision_mask = 4 | 32  # Environment and Interactables

		var result = space_state.intersect_ray(query)
		if result:
			var hit_object = result.collider
			if hit_object.has_method("interact"):
				hit_object.interact()

## Keyboard Input (Fallback)
func _handle_keyboard_input(event: InputEventKey) -> void:
	if not event.pressed:
		return
	
	# Movement keys
	var keyboard_direction = Vector2.ZERO
	
	if Input.is_action_pressed("move_up"):
		keyboard_direction.y -= 1
	if Input.is_action_pressed("move_down"):
		keyboard_direction.y += 1
	if Input.is_action_pressed("move_left"):
		keyboard_direction.x -= 1
	if Input.is_action_pressed("move_right"):
		keyboard_direction.x += 1
	
	if keyboard_direction != current_movement_direction:
		current_movement_direction = keyboard_direction.normalized()
		movement_input.emit(current_movement_direction)
	
	# Skill keys
	if Input.is_action_just_pressed("skill_1"):
		skill_activated.emit("skill_1")
	elif Input.is_action_just_pressed("skill_2"):
		skill_activated.emit("skill_2")

func _process(delta: float) -> void:
	# Update keyboard movement continuously
	if not virtual_joystick_active:
		_update_keyboard_movement()

func _update_keyboard_movement() -> void:
	var keyboard_direction = Vector2.ZERO
	
	if Input.is_action_pressed("move_up"):
		keyboard_direction.y -= 1
	if Input.is_action_pressed("move_down"):
		keyboard_direction.y += 1
	if Input.is_action_pressed("move_left"):
		keyboard_direction.x -= 1
	if Input.is_action_pressed("move_right"):
		keyboard_direction.x += 1
	
	keyboard_direction = keyboard_direction.normalized()
	
	if keyboard_direction != current_movement_direction:
		current_movement_direction = keyboard_direction
		movement_input.emit(current_movement_direction)

## Utility Functions
func get_current_input_direction() -> Vector2:
	return current_movement_direction

func is_using_touch_input() -> bool:
	return virtual_joystick_active or active_touches.size() > 0

func set_joystick_sensitivity(sensitivity: float) -> void:
	joystick_sensitivity = clamp(sensitivity, 0.1, 2.0)

func set_joystick_deadzone(deadzone: float) -> void:
	joystick_deadzone = clamp(deadzone, 0.0, 0.5)

## Debug Functions
func debug_print_input_state() -> void:
	print("=== Input Debug Info ===")
	print("Movement Direction: %s" % current_movement_direction)
	print("Virtual Joystick Active: %s" % virtual_joystick_active)
	print("Active Touches: %d" % active_touches.size())
	print("Joystick Center: %s" % joystick_center)
	print("========================")
