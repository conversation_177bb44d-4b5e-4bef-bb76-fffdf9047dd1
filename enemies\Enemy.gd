class_name Enemy
extends CharacterBody3D

## Base enemy class implementing AI behaviors and combat mechanics
## Uses State Machine pattern for AI decision making

signal enemy_died(enemy: Enemy, experience: int, loot_data: Dictionary)
signal enemy_damaged(enemy: Enemy, damage: int)
signal enemy_spawned(enemy: Enemy)

@export var enemy_type: GameEnums.EnemyType = GameEnums.EnemyType.GRUNT
@export var behavior_type: GameEnums.EnemyBehavior = GameEnums.EnemyBehavior.AGGRESSIVE
@export var level: int = 1
@export var move_speed: float = 3.0
@export var attack_range: float = 2.0
@export var detection_range: float = 8.0
@export var attack_cooldown: float = 2.0

# Stats
var max_health: int = 50
var current_health: int = 50
var attack_damage: int = 10
var defense: int = 2
var experience_reward: int = 10
var gold_reward: int = 5

# AI State
var current_ai_state: GameEnums.AIState = GameEnums.AIState.IDLE
var target_player: Node3D = null
var last_known_player_position: Vector3 = Vector3.ZERO
var attack_timer: float = 0.0
var state_timer: float = 0.0

# Movement
var patrol_points: Array[Vector3] = []
var current_patrol_index: int = 0
var home_position: Vector3 = Vector3.ZERO
var max_chase_distance: float = 15.0

# Components
@onready var navigation_agent: NavigationAgent3D = $NavigationAgent3D
@onready var detection_area: Area3D = $DetectionArea
@onready var attack_area: Area3D = $AttackArea
@onready var health_bar: ProgressBar = $HealthBar
@onready var mesh_instance: MeshInstance3D = $MeshInstance3D

func _ready() -> void:
	_setup_enemy()
	_connect_signals()
	_initialize_ai()

func _setup_enemy() -> void:
	# Set collision layers
	collision_layer = 2  # Enemy layer
	collision_mask = 1 | 4  # Player and Environment
	
	# Add to enemy group
	add_to_group("enemies")
	
	# Store home position
	home_position = global_position
	
	# Scale stats by level
	_scale_stats_by_level()
	
	# Setup navigation
	if navigation_agent:
		navigation_agent.target_desired_distance = 1.0
		navigation_agent.path_desired_distance = 0.5

func _connect_signals() -> void:
	if detection_area:
		detection_area.body_entered.connect(_on_detection_area_entered)
		detection_area.body_exited.connect(_on_detection_area_exited)
	
	if attack_area:
		attack_area.body_entered.connect(_on_attack_area_entered)
		attack_area.body_exited.connect(_on_attack_area_exited)
	
	enemy_spawned.emit(self)

func _scale_stats_by_level() -> void:
	var level_multiplier = 1.0 + (level - 1) * 0.2  # 20% increase per level
	
	max_health = int(max_health * level_multiplier)
	current_health = max_health
	attack_damage = int(attack_damage * level_multiplier)
	defense = int(defense * level_multiplier)
	experience_reward = int(experience_reward * level_multiplier)
	gold_reward = int(gold_reward * level_multiplier)

func _initialize_ai() -> void:
	# Set up patrol points for patrol behavior
	if behavior_type == GameEnums.EnemyBehavior.DEFENSIVE:
		_generate_patrol_points()
	
	# Start in idle state
	_change_ai_state(GameEnums.AIState.IDLE)

func _generate_patrol_points() -> void:
	# Generate random patrol points around home position
	for i in range(3):
		var angle = (i * TAU) / 3.0
		var distance = randf_range(3.0, 6.0)
		var patrol_point = home_position + Vector3(
			cos(angle) * distance,
			0,
			sin(angle) * distance
		)
		patrol_points.append(patrol_point)

func _physics_process(delta: float) -> void:
	_update_ai(delta)
	_update_timers(delta)
	_update_movement(delta)
	_update_health_bar()

## AI State Machine
func _update_ai(delta: float) -> void:
	state_timer += delta
	
	match current_ai_state:
		GameEnums.AIState.IDLE:
			_ai_idle()
		GameEnums.AIState.PATROL:
			_ai_patrol()
		GameEnums.AIState.CHASE:
			_ai_chase()
		GameEnums.AIState.ATTACK:
			_ai_attack()
		GameEnums.AIState.RETREAT:
			_ai_retreat()

func _ai_idle() -> void:
	# Look for player or start patrolling
	if target_player:
		_change_ai_state(GameEnums.AIState.CHASE)
	elif behavior_type == GameEnums.EnemyBehavior.DEFENSIVE and state_timer > 2.0:
		_change_ai_state(GameEnums.AIState.PATROL)

func _ai_patrol() -> void:
	if target_player:
		_change_ai_state(GameEnums.AIState.CHASE)
		return
	
	# Move to next patrol point
	if patrol_points.size() > 0:
		var target_point = patrol_points[current_patrol_index]
		navigation_agent.target_position = target_point
		
		if global_position.distance_to(target_point) < 2.0:
			current_patrol_index = (current_patrol_index + 1) % patrol_points.size()
			state_timer = 0.0

func _ai_chase() -> void:
	if not target_player or not is_instance_valid(target_player):
		_change_ai_state(GameEnums.AIState.IDLE)
		return
	
	var distance_to_player = global_position.distance_to(target_player.global_position)
	
	# Check if player is too far away
	if distance_to_player > max_chase_distance:
		_change_ai_state(GameEnums.AIState.RETREAT)
		return
	
	# Check if in attack range
	if distance_to_player <= attack_range:
		_change_ai_state(GameEnums.AIState.ATTACK)
		return
	
	# Move towards player
	navigation_agent.target_position = target_player.global_position
	last_known_player_position = target_player.global_position

func _ai_attack() -> void:
	if not target_player or not is_instance_valid(target_player):
		_change_ai_state(GameEnums.AIState.IDLE)
		return
	
	var distance_to_player = global_position.distance_to(target_player.global_position)
	
	# Check if player moved out of range
	if distance_to_player > attack_range * 1.2:
		_change_ai_state(GameEnums.AIState.CHASE)
		return
	
	# Face the player
	var direction_to_player = (target_player.global_position - global_position).normalized()
	rotation.y = atan2(direction_to_player.x, direction_to_player.z)
	
	# Attack if cooldown is ready
	if attack_timer <= 0:
		_perform_attack()

func _ai_retreat() -> void:
	# Return to home position
	navigation_agent.target_position = home_position
	
	if global_position.distance_to(home_position) < 2.0:
		_change_ai_state(GameEnums.AIState.IDLE)
		target_player = null

func _change_ai_state(new_state: GameEnums.AIState) -> void:
	current_ai_state = new_state
	state_timer = 0.0

## Combat System
func _perform_attack() -> void:
	if not target_player:
		return
	
	attack_timer = attack_cooldown
	
	# Calculate damage
	var damage = _calculate_attack_damage()
	
	# Deal damage to player
	if target_player.has_method("take_damage"):
		target_player.take_damage(damage)
	
	# Visual effects
	EventBus.effect_requested.emit("enemy_attack", global_position)
	EventBus.damage_dealt.emit(target_player, damage, "physical")
	
	print("Enemy attacked player for %d damage" % damage)

func _calculate_attack_damage() -> int:
	var base_damage = attack_damage
	var variance = base_damage * 0.2  # ±20% variance
	return int(randf_range(base_damage - variance, base_damage + variance))

func take_damage(damage: int, damage_type: String = "physical") -> void:
	# Apply defense
	var final_damage = max(1, damage - defense)
	current_health -= final_damage
	
	# Emit signals
	enemy_damaged.emit(self, final_damage)
	EventBus.damage_dealt.emit(self, final_damage, damage_type)
	
	# Visual feedback
	_show_damage_number(final_damage)
	_flash_damage_effect()
	
	# Check for death
	if current_health <= 0:
		_die()
	else:
		# Become aggressive when damaged
		if current_ai_state == GameEnums.AIState.IDLE or current_ai_state == GameEnums.AIState.PATROL:
			_change_ai_state(GameEnums.AIState.CHASE)

func _die() -> void:
	# Calculate rewards
	var loot_data = _calculate_loot_rewards()
	
	# Emit death signal
	enemy_died.emit(self, experience_reward, loot_data)
	EventBus.enemy_killed.emit(self, experience_reward)
	
	# Visual effects
	EventBus.effect_requested.emit("enemy_death", global_position)
	
	# Remove from scene
	queue_free()

func _calculate_loot_rewards() -> Dictionary:
	var loot = {
		"gold": gold_reward,
		"items": []
	}
	
	# Chance for item drops based on enemy type
	var drop_chance = 0.1  # 10% base chance
	
	match enemy_type:
		GameEnums.EnemyType.ELITE:
			drop_chance = 0.25
		GameEnums.EnemyType.BOSS:
			drop_chance = 0.8
		GameEnums.EnemyType.WORLD_BOSS:
			drop_chance = 1.0
	
	if randf() < drop_chance:
		var item_rarity = _roll_item_rarity()
		loot.items.append({
			"rarity": item_rarity,
			"level": level
		})
	
	return loot

func _roll_item_rarity() -> GameEnums.ItemRarity:
	var roll = randf() * 100.0
	
	# Adjust chances based on enemy type
	var rarity_bonus = 0
	match enemy_type:
		GameEnums.EnemyType.ELITE:
			rarity_bonus = 1
		GameEnums.EnemyType.BOSS:
			rarity_bonus = 2
		GameEnums.EnemyType.WORLD_BOSS:
			rarity_bonus = 3
	
	if roll < 5.0:  # 5% for rare+
		return min(GameEnums.ItemRarity.RARE + rarity_bonus, GameEnums.ItemRarity.DIVINE)
	elif roll < 20.0:  # 15% for uncommon+
		return min(GameEnums.ItemRarity.UNCOMMON + rarity_bonus, GameEnums.ItemRarity.DIVINE)
	else:
		return min(GameEnums.ItemRarity.COMMON + rarity_bonus, GameEnums.ItemRarity.DIVINE)

## Movement and Navigation
func _update_movement(delta: float) -> void:
	if current_ai_state == GameEnums.AIState.ATTACK:
		# Don't move while attacking
		velocity = Vector3.ZERO
	elif navigation_agent.is_navigation_finished():
		velocity = Vector3.ZERO
	else:
		var next_path_position = navigation_agent.get_next_path_position()
		var direction = (next_path_position - global_position).normalized()
		velocity = direction * move_speed
	
	# Apply gravity
	if not is_on_floor():
		velocity.y += get_gravity().y * delta
	
	move_and_slide()

func _update_timers(delta: float) -> void:
	if attack_timer > 0:
		attack_timer -= delta

## Visual Effects
func _show_damage_number(damage: int) -> void:
	# Request damage number from resource manager
	var damage_number = ResourceManager.get_pooled_object("damage_number")
	if damage_number:
		get_parent().add_child(damage_number)
		damage_number.global_position = global_position + Vector3(0, 2, 0)
		damage_number.setup(damage, Color.RED)

func _flash_damage_effect() -> void:
	if mesh_instance:
		var tween = create_tween()
		tween.tween_property(mesh_instance, "modulate", Color.RED, 0.1)
		tween.tween_property(mesh_instance, "modulate", Color.WHITE, 0.1)

func _update_health_bar() -> void:
	if health_bar:
		health_bar.max_value = max_health
		health_bar.value = current_health
		health_bar.visible = current_health < max_health

## Detection System
func _on_detection_area_entered(body: Node3D) -> void:
	if body.is_in_group("player"):
		target_player = body
		if current_ai_state == GameEnums.AIState.IDLE or current_ai_state == GameEnums.AIState.PATROL:
			_change_ai_state(GameEnums.AIState.CHASE)

func _on_detection_area_exited(body: Node3D) -> void:
	if body == target_player and current_ai_state != GameEnums.AIState.ATTACK:
		# Don't immediately lose target, use last known position
		pass

func _on_attack_area_entered(body: Node3D) -> void:
	if body == target_player and current_ai_state == GameEnums.AIState.CHASE:
		_change_ai_state(GameEnums.AIState.ATTACK)

func _on_attack_area_exited(body: Node3D) -> void:
	if body == target_player and current_ai_state == GameEnums.AIState.ATTACK:
		_change_ai_state(GameEnums.AIState.CHASE)

## Utility Functions
func get_health_percentage() -> float:
	return float(current_health) / float(max_health)

func is_alive() -> bool:
	return current_health > 0

func get_enemy_type_name() -> String:
	return GameEnums.EnemyType.keys()[enemy_type]

func set_level(new_level: int) -> void:
	level = new_level
	_scale_stats_by_level()

## Debug Functions
func debug_print_ai_state() -> void:
	print("Enemy AI State: %s, Health: %d/%d, Target: %s" % [
		GameEnums.AIState.keys()[current_ai_state],
		current_health,
		max_health,
		target_player.name if target_player else "None"
	])
