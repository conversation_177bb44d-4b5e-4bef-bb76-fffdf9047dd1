class_name Accessory
extends Item

## Accessory class implementing jewelry and trinket functionality
## Provides passive bonuses and special effects for character enhancement

@export var accessory_type: String = "ring"  # ring, amulet, charm, trinket
@export var set_name: String = ""  # For set bonuses
@export var enchantment_level: int = 0
@export var special_effects: Array[String] = []

# Accessory-specific properties
@export var passive_abilities: Array[String] = []
@export var trigger_chance: float = 0.0  # For proc effects
@export var cooldown_duration: float = 0.0
@export var aura_radius: float = 0.0  # For aura effects

# Set bonus tracking
var set_pieces_equipped: int = 0
var set_bonus_active: bool = false

func _init() -> void:
	super._init()
	item_type = GameEnums.ItemType.ACCESSORY

func _setup_base_properties() -> void:
	super._setup_base_properties()
	_setup_accessory_defaults()

func _setup_accessory_defaults() -> void:
	# Set default properties based on accessory type
	match accessory_type:
		"ring":
			socket_count = 1
			max_durability = 0  # Rings don't break
		"amulet":
			socket_count = 2
			max_durability = 0  # Amulets don't break
		"charm":
			socket_count = 0
			max_durability = 50  # Charms are fragile
		"trinket":
			socket_count = 1
			max_durability = 75

## Equipment System Override
func can_equip() -> bool:
	if not super.can_equip():
		return false
	
	# Check if player meets level requirement
	var player_level = PlayerManager.get_player_level() if PlayerManager else 1
	return player_level >= level_requirement

func get_equipment_slot() -> String:
	return "accessory_%s" % accessory_type

func _apply_equipment_effects() -> void:
	super._apply_equipment_effects()
	_activate_passive_abilities()
	_check_set_bonus()

func _remove_equipment_effects() -> void:
	super._remove_equipment_effects()
	_deactivate_passive_abilities()
	_remove_set_bonus()

## Passive Abilities System
func _activate_passive_abilities() -> void:
	for ability in passive_abilities:
		_activate_passive_ability(ability)

func _deactivate_passive_abilities() -> void:
	for ability in passive_abilities:
		_deactivate_passive_ability(ability)

func _activate_passive_ability(ability: String) -> void:
	match ability:
		"health_regeneration":
			EventBus.passive_effect_applied.emit("health_regen", get_stat_value(GameEnums.StatType.HEALTH) * 0.01)
		"mana_regeneration":
			EventBus.passive_effect_applied.emit("mana_regen", get_stat_value(GameEnums.StatType.MANA) * 0.01)
		"damage_reflection":
			EventBus.passive_effect_applied.emit("damage_reflect", trigger_chance)
		"critical_aura":
			EventBus.aura_effect_applied.emit("crit_bonus", aura_radius, get_stat_value(GameEnums.StatType.CRIT_CHANCE))
		"experience_boost":
			EventBus.passive_effect_applied.emit("exp_multiplier", 1.0 + get_stat_value(GameEnums.StatType.EXPERIENCE_GAIN))

func _deactivate_passive_ability(ability: String) -> void:
	match ability:
		"health_regeneration":
			EventBus.passive_effect_removed.emit("health_regen")
		"mana_regeneration":
			EventBus.passive_effect_removed.emit("mana_regen")
		"damage_reflection":
			EventBus.passive_effect_removed.emit("damage_reflect")
		"critical_aura":
			EventBus.aura_effect_removed.emit("crit_bonus")
		"experience_boost":
			EventBus.passive_effect_removed.emit("exp_multiplier")

## Set Bonus System
func _check_set_bonus() -> void:
	if set_name.is_empty():
		return
	
	# Count equipped set pieces (would need InventoryManager integration)
	set_pieces_equipped = _count_equipped_set_pieces()
	
	# Activate set bonuses based on pieces equipped
	if set_pieces_equipped >= 2 and not set_bonus_active:
		_activate_set_bonus(2)
	elif set_pieces_equipped >= 4 and set_bonus_active:
		_upgrade_set_bonus(4)
	elif set_pieces_equipped >= 6:
		_upgrade_set_bonus(6)

func _count_equipped_set_pieces() -> int:
	# This would integrate with InventoryManager to count equipped set pieces
	# For now, return 1 (this item)
	return 1

func _activate_set_bonus(pieces: int) -> void:
	set_bonus_active = true
	match set_name:
		"arcane_mastery":
			if pieces >= 2:
				EventBus.set_bonus_applied.emit("arcane_mastery_2", {"mana_regen": 5})
			if pieces >= 4:
				EventBus.set_bonus_applied.emit("arcane_mastery_4", {"spell_damage": 0.15})
		"warrior_might":
			if pieces >= 2:
				EventBus.set_bonus_applied.emit("warrior_might_2", {"attack_speed": 0.1})
			if pieces >= 4:
				EventBus.set_bonus_applied.emit("warrior_might_4", {"crit_damage": 0.25})

func _upgrade_set_bonus(pieces: int) -> void:
	_activate_set_bonus(pieces)

func _remove_set_bonus() -> void:
	if set_bonus_active:
		EventBus.set_bonus_removed.emit(set_name)
		set_bonus_active = false

## Special Effects
func trigger_special_effect(effect_name: String, context: Dictionary = {}) -> bool:
	if not special_effects.has(effect_name):
		return false
	
	# Check cooldown and trigger chance
	if randf() > trigger_chance:
		return false
	
	match effect_name:
		"lightning_strike":
			return _trigger_lightning_strike(context)
		"healing_burst":
			return _trigger_healing_burst(context)
		"shield_barrier":
			return _trigger_shield_barrier(context)
		"time_dilation":
			return _trigger_time_dilation(context)
	
	return false

func _trigger_lightning_strike(context: Dictionary) -> bool:
	var damage = get_stat_value(GameEnums.StatType.ATTACK) * 0.5
	EventBus.effect_requested.emit("lightning_strike", context.get("position", Vector3.ZERO), {"damage": damage})
	return true

func _trigger_healing_burst(context: Dictionary) -> bool:
	var heal_amount = get_stat_value(GameEnums.StatType.HEALTH) * 0.1
	EventBus.effect_requested.emit("healing_burst", context.get("position", Vector3.ZERO), {"heal": heal_amount})
	return true

func _trigger_shield_barrier(context: Dictionary) -> bool:
	var shield_strength = get_stat_value(GameEnums.StatType.DEFENSE) * 0.3
	EventBus.effect_requested.emit("shield_barrier", context.get("position", Vector3.ZERO), {"shield": shield_strength})
	return true

func _trigger_time_dilation(context: Dictionary) -> bool:
	EventBus.effect_requested.emit("time_dilation", context.get("position", Vector3.ZERO), {"duration": 3.0})
	return true

## Enchantment System
func can_enchant() -> bool:
	return enchantment_level < 5  # Max enchantment level

func enchant(enchantment_type: String, level: int = 1) -> bool:
	if not can_enchant():
		return false
	
	enchantment_level += level
	_apply_enchantment(enchantment_type, level)
	return true

func _apply_enchantment(enchantment_type: String, level: int) -> void:
	match enchantment_type:
		"power":
			stat_modifiers[GameEnums.StatType.ATTACK] += level * 2
		"protection":
			stat_modifiers[GameEnums.StatType.DEFENSE] += level * 2
		"vitality":
			stat_modifiers[GameEnums.StatType.HEALTH] += level * 10
		"wisdom":
			stat_modifiers[GameEnums.StatType.MANA] += level * 5

## Utility Functions
func get_accessory_type_name() -> String:
	return accessory_type.replace("_", " ").capitalize()

func get_set_bonus_description() -> String:
	if set_name.is_empty():
		return ""
	
	var desc = "Set: %s (%d/6)\n" % [set_name.replace("_", " ").capitalize(), set_pieces_equipped]
	desc += "2 pieces: Minor bonus\n"
	desc += "4 pieces: Major bonus\n"
	desc += "6 pieces: Legendary bonus"
	return desc

func is_part_of_set() -> bool:
	return not set_name.is_empty()

func get_tooltip_text() -> String:
	var tooltip = super.get_tooltip_text()
	
	# Add accessory-specific info
	tooltip += "\nType: %s" % get_accessory_type_name()
	
	if enchantment_level > 0:
		tooltip += "\nEnchantment Level: +%d" % enchantment_level
	
	if not passive_abilities.is_empty():
		tooltip += "\nPassive Effects:"
		for ability in passive_abilities:
			tooltip += "\n• %s" % ability.replace("_", " ").capitalize()
	
	if is_part_of_set():
		tooltip += "\n\n%s" % get_set_bonus_description()
	
	return tooltip
