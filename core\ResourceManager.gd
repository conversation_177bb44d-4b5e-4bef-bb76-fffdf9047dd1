extends Node

## Asset loading and caching system for optimal performance
## Implements resource pooling and asynchronous loading for mobile optimization

var loaded_resources: Dictionary = {}
var loading_queue: Array = []
var resource_pools: Dictionary = {}
var is_loading: bool = false

# Resource categories for organized loading
enum ResourceType {
	TEXTURE,
	MESH,
	MATERIAL,
	AUDIO,
	SCENE,
	SHADER,
	ANIMATION
}

# Essential resources to preload
var essential_resources: Dictionary = {
	"player_model": "res://models/player/player.glb",
	"ui_theme": "res://ui/themes/main_theme.tres",
	"default_material": "res://materials/default.tres",
	"hit_sound": "res://audio/sfx/hit.ogg",
	"ui_click": "res://audio/sfx/ui_click.ogg"
}

# Resource pools for frequently used objects
var pooled_resource_configs: Dictionary = {
	"damage_number": {
		"scene_path": "res://ui/DamageNumber.tscn",
		"pool_size": 20
	},
	"hit_effect": {
		"scene_path": "res://vfx/HitEffect.tscn",
		"pool_size": 15
	},
	"loot_pickup": {
		"scene_path": "res://items/LootPickup.tscn",
		"pool_size": 10
	}
}

func _ready() -> void:
	_initialize_resource_pools()
	print("ResourceManager initialized")

func _initialize_resource_pools() -> void:
	for pool_name in pooled_resource_configs:
		var config = pooled_resource_configs[pool_name]
		_create_resource_pool(pool_name, config.scene_path, config.pool_size)

func _create_resource_pool(pool_name: String, scene_path: String, pool_size: int) -> void:
	resource_pools[pool_name] = {
		"available": [],
		"in_use": [],
		"scene_path": scene_path,
		"total_created": 0
	}
	
	# Pre-instantiate pool objects
	var scene_resource = load(scene_path)
	if scene_resource:
		for i in pool_size:
			var instance = scene_resource.instantiate()
			instance.set_process(false)
			instance.visible = false
			resource_pools[pool_name].available.append(instance)
			resource_pools[pool_name].total_created += 1
		
		print("ResourceManager: Created pool '%s' with %d objects" % [pool_name, pool_size])

## Core Resource Loading
func preload_essential_resources() -> void:
	print("ResourceManager: Preloading essential resources...")
	
	for resource_name in essential_resources:
		var path = essential_resources[resource_name]
		var resource = load(path)
		
		if resource:
			loaded_resources[resource_name] = resource
			print("  ✓ Loaded: %s" % resource_name)
		else:
			print("  ✗ Failed to load: %s at %s" % [resource_name, path])
	
	print("ResourceManager: Essential resources loaded (%d/%d)" % [
		loaded_resources.size(), 
		essential_resources.size()
	])

func load_resource_async(path: String, callback: Callable) -> void:
	if loaded_resources.has(path):
		callback.call(loaded_resources[path])
		return
	
	loading_queue.append({
		"path": path,
		"callback": callback
	})
	
	if not is_loading:
		_process_loading_queue()

func _process_loading_queue() -> void:
	if loading_queue.is_empty():
		is_loading = false
		return
	
	is_loading = true
	var load_request = loading_queue.pop_front()
	
	# Use ResourceLoader for async loading in production
	var resource = load(load_request.path)
	
	if resource:
		loaded_resources[load_request.path] = resource
		load_request.callback.call(resource)
	else:
		print("ResourceManager: Failed to load resource: %s" % load_request.path)
		load_request.callback.call(null)
	
	# Continue processing queue
	call_deferred("_process_loading_queue")

func get_resource(path: String) -> Resource:
	if loaded_resources.has(path):
		return loaded_resources[path]
	
	# Synchronous fallback
	var resource = load(path)
	if resource:
		loaded_resources[path] = resource
	
	return resource

func cache_resource(name: String, resource: Resource) -> void:
	loaded_resources[name] = resource

func unload_resource(path: String) -> void:
	if loaded_resources.has(path):
		loaded_resources.erase(path)
		print("ResourceManager: Unloaded resource: %s" % path)

## Object Pooling System
func get_pooled_object(pool_name: String) -> Node:
	if not resource_pools.has(pool_name):
		print("ResourceManager: Pool '%s' does not exist" % pool_name)
		return null
	
	var pool = resource_pools[pool_name]
	
	if pool.available.is_empty():
		# Create new object if pool is empty
		var scene_resource = get_resource(pool.scene_path)
		if scene_resource:
			var new_instance = scene_resource.instantiate()
			pool.total_created += 1
			pool.in_use.append(new_instance)
			return new_instance
		else:
			print("ResourceManager: Failed to create new pooled object for '%s'" % pool_name)
			return null
	
	# Get object from available pool
	var obj = pool.available.pop_back()
	pool.in_use.append(obj)
	
	# Reset object state
	obj.set_process(true)
	obj.visible = true
	
	return obj

func return_pooled_object(pool_name: String, obj: Node) -> void:
	if not resource_pools.has(pool_name):
		print("ResourceManager: Cannot return object to non-existent pool '%s'" % pool_name)
		return
	
	var pool = resource_pools[pool_name]
	var index = pool.in_use.find(obj)
	
	if index == -1:
		print("ResourceManager: Object not found in pool '%s' in_use list" % pool_name)
		return
	
	# Remove from in_use and add to available
	pool.in_use.remove_at(index)
	pool.available.append(obj)
	
	# Reset object state
	obj.set_process(false)
	obj.visible = false
	
	# Remove from scene tree if attached
	if obj.get_parent():
		obj.get_parent().remove_child(obj)

## Specialized Resource Functions
func get_texture(path: String) -> Texture2D:
	var resource = get_resource(path)
	if resource is Texture2D:
		return resource
	return null

func get_mesh(path: String) -> Mesh:
	var resource = get_resource(path)
	if resource is Mesh:
		return resource
	return null

func get_material(path: String) -> Material:
	var resource = get_resource(path)
	if resource is Material:
		return resource
	return null

func get_audio_stream(path: String) -> AudioStream:
	var resource = get_resource(path)
	if resource is AudioStream:
		return resource
	return null

func get_scene(path: String) -> PackedScene:
	var resource = get_resource(path)
	if resource is PackedScene:
		return resource
	return null

## Memory Management
func clear_unused_resources() -> void:
	var cleared_count = 0
	var resources_to_remove = []
	
	for path in loaded_resources:
		var resource = loaded_resources[path]
		if resource.get_reference_count() <= 1:  # Only referenced by our cache
			resources_to_remove.append(path)
	
	for path in resources_to_remove:
		loaded_resources.erase(path)
		cleared_count += 1
	
	print("ResourceManager: Cleared %d unused resources" % cleared_count)

func get_memory_usage() -> Dictionary:
	var total_resources = loaded_resources.size()
	var pool_objects = 0
	
	for pool_name in resource_pools:
		var pool = resource_pools[pool_name]
		pool_objects += pool.total_created
	
	return {
		"loaded_resources": total_resources,
		"pooled_objects": pool_objects,
		"loading_queue_size": loading_queue.size()
	}

## Debug Functions
func debug_print_pools() -> void:
	print("=== ResourceManager Pool Status ===")
	for pool_name in resource_pools:
		var pool = resource_pools[pool_name]
		print("%s: %d available, %d in use, %d total" % [
			pool_name,
			pool.available.size(),
			pool.in_use.size(),
			pool.total_created
		])
	print("==================================")

func debug_print_loaded_resources() -> void:
	print("=== Loaded Resources ===")
	for path in loaded_resources:
		var resource = loaded_resources[path]
		print("%s: %s (refs: %d)" % [
			path,
			resource.get_class(),
			resource.get_reference_count()
		])
	print("========================")

## Cleanup
func _exit_tree() -> void:
	# Clean up all pooled objects
	for pool_name in resource_pools:
		var pool = resource_pools[pool_name]
		for obj in pool.available:
			if is_instance_valid(obj):
				obj.queue_free()
		for obj in pool.in_use:
			if is_instance_valid(obj):
				obj.queue_free()
	
	resource_pools.clear()
	loaded_resources.clear()
	loading_queue.clear()
	
	print("ResourceManager: Cleanup completed")
