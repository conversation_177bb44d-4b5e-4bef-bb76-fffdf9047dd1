class_name ItemFactory
extends RefCounted

## Factory pattern implementation for creating items
## Handles item generation, stat rolling, and rarity determination

static var item_templates: Dictionary = {}
static var rng: RandomNumberGenerator = RandomNumberGenerator.new()

# Item generation weights by type
static var type_weights: Dictionary = {
	GameEnums.ItemType.WEAPON: 25,
	GameEnums.ItemType.ARMOR: 25,
	GameEnums.ItemType.WINGS: 15,
	GameEnums.ItemType.ACCESSORY: 20,
	GameEnums.ItemType.CONSUMABLE: 10,
	GameEnums.ItemType.MATERIAL: 5
}

# Stat ranges by rarity
static var stat_ranges: Dictionary = {
	GameEnums.ItemRarity.COMMON: {"min": 1, "max": 3},
	GameEnums.ItemRarity.UNCOMMON: {"min": 2, "max": 5},
	GameEnums.ItemRarity.RARE: {"min": 4, "max": 8},
	GameEnums.ItemRarity.EPIC: {"min": 7, "max": 12},
	GameEnums.ItemRarity.LEGENDARY: {"min": 10, "max": 18},
	GameEnums.ItemRarity.MYTHIC: {"min": 15, "max": 25},
	GameEnums.ItemRarity.DIVINE: {"min": 20, "max": 35}
}

static func _static_init() -> void:
	rng.randomize()
	_load_item_templates()

static func _load_item_templates() -> void:
	# Load item templates from data files
	# This would typically load from JSON or Godot resource files
	_create_weapon_templates()
	_create_armor_templates()
	_create_wing_templates()
	_create_accessory_templates()
	_create_consumable_templates()

static func _create_weapon_templates() -> void:
	item_templates["sword_basic"] = {
		"name": "Iron Sword",
		"description": "A sturdy iron blade for combat",
		"item_type": GameEnums.ItemType.WEAPON,
		"weapon_type": GameEnums.WeaponType.SWORD,
		"base_stats": {GameEnums.StatType.ATTACK: 10},
		"socket_count": 1,
		"level_requirement": 1
	}
	
	item_templates["staff_basic"] = {
		"name": "Wooden Staff",
		"description": "A simple staff imbued with magical energy",
		"item_type": GameEnums.ItemType.WEAPON,
		"weapon_type": GameEnums.WeaponType.STAFF,
		"base_stats": {GameEnums.StatType.ATTACK: 8, GameEnums.StatType.MANA: 20},
		"socket_count": 2,
		"level_requirement": 1
	}

static func _create_armor_templates() -> void:
	item_templates["leather_armor"] = {
		"name": "Leather Armor",
		"description": "Basic protection made from treated leather",
		"item_type": GameEnums.ItemType.ARMOR,
		"armor_type": GameEnums.ArmorType.LIGHT,
		"base_stats": {GameEnums.StatType.DEFENSE: 5, GameEnums.StatType.HEALTH: 20},
		"socket_count": 1,
		"level_requirement": 1
	}

static func _create_wing_templates() -> void:
	item_templates["feather_wings"] = {
		"name": "Feather Wings",
		"description": "Light wings that grant the power of flight",
		"item_type": GameEnums.ItemType.WINGS,
		"base_stats": {GameEnums.StatType.MOVE_SPEED: 1.0},
		"socket_count": 2,
		"level_requirement": 5
	}

static func _create_accessory_templates() -> void:
	item_templates["silver_ring"] = {
		"name": "Silver Ring",
		"description": "A simple ring with minor magical properties",
		"item_type": GameEnums.ItemType.ACCESSORY,
		"base_stats": {GameEnums.StatType.CRIT_CHANCE: 0.02},
		"socket_count": 1,
		"level_requirement": 3
	}

static func _create_consumable_templates() -> void:
	item_templates["health_potion"] = {
		"name": "Health Potion",
		"description": "Restores health when consumed",
		"item_type": GameEnums.ItemType.CONSUMABLE,
		"is_consumable": true,
		"stack_size": 10,
		"heal_amount": 50,
		"level_requirement": 1
	}

## Main Factory Methods
static func create_item(template_id: String, rarity: GameEnums.ItemRarity = GameEnums.ItemRarity.COMMON, level: int = 1) -> Item:
	if not item_templates.has(template_id):
		print("ItemFactory: Template '%s' not found" % template_id)
		return null
	
	var template = item_templates[template_id]
	var item: Item
	
	# Create appropriate item subclass based on type
	match template.item_type:
		GameEnums.ItemType.WEAPON:
			item = Weapon.new()
		GameEnums.ItemType.ARMOR:
			item = Armor.new()
		GameEnums.ItemType.WINGS:
			item = Wings.new()
		GameEnums.ItemType.ACCESSORY:
			item = Accessory.new()
		GameEnums.ItemType.CONSUMABLE:
			item = Consumable.new()
		_:
			item = Item.new()
	
	_apply_template_to_item(item, template, rarity, level)
	return item

static func create_random_item(player_level: int = 1, force_type: GameEnums.ItemType = GameEnums.ItemType.WEAPON) -> Item:
	var rarity = _roll_rarity()
	var item_type = force_type if force_type != GameEnums.ItemType.WEAPON else _roll_item_type()
	var template_id = _get_random_template_for_type(item_type)
	
	if template_id.is_empty():
		return null
	
	return create_item(template_id, rarity, player_level)

static func create_loot_drop(enemy_level: int, enemy_type: GameEnums.EnemyType) -> Item:
	var rarity = _roll_loot_rarity(enemy_type)
	var item_type = _roll_item_type()
	var template_id = _get_random_template_for_type(item_type)
	
	if template_id.is_empty():
		return null
	
	var item = create_item(template_id, rarity, enemy_level)
	
	# Apply loot-specific modifiers
	if enemy_type == GameEnums.EnemyType.BOSS:
		_apply_boss_loot_bonus(item)
	elif enemy_type == GameEnums.EnemyType.ELITE:
		_apply_elite_loot_bonus(item)
	
	return item

## Template Application
static func _apply_template_to_item(item: Item, template: Dictionary, rarity: GameEnums.ItemRarity, level: int) -> void:
	# Basic properties
	item.id = _generate_item_id(template, rarity)
	item.name = _generate_item_name(template, rarity)
	item.description = template.get("description", "")
	item.item_type = template.get("item_type", GameEnums.ItemType.CONSUMABLE)
	item.rarity = rarity
	item.level_requirement = max(1, template.get("level_requirement", 1) + _get_level_offset(rarity))
	item.socket_count = template.get("socket_count", 0)
	item.stack_size = template.get("stack_size", 1)
	item.is_consumable = template.get("is_consumable", false)
	
	# Calculate sell value based on rarity and level
	item.sell_value = _calculate_sell_value(rarity, level)
	
	# Apply base stats with rarity scaling
	var base_stats = template.get("base_stats", {})
	item.stat_modifiers = _scale_stats_for_rarity(base_stats, rarity, level)
	
	# Apply type-specific properties
	_apply_type_specific_properties(item, template)
	
	# Initialize sockets
	item._setup_sockets()

static func _apply_type_specific_properties(item: Item, template: Dictionary) -> void:
	match item.item_type:
		GameEnums.ItemType.WEAPON:
			if item is Weapon:
				item.weapon_type = template.get("weapon_type", GameEnums.WeaponType.SWORD)
				item.attack_speed = template.get("attack_speed", 1.0)
				item.range = template.get("range", GameConstants.AUTO_ATTACK_RANGE)
		
		GameEnums.ItemType.ARMOR:
			if item is Armor:
				item.armor_type = template.get("armor_type", GameEnums.ArmorType.LIGHT)
		
		GameEnums.ItemType.CONSUMABLE:
			if item is Consumable:
				item.heal_amount = template.get("heal_amount", 0)
				item.mana_amount = template.get("mana_amount", 0)
				item.buff_duration = template.get("buff_duration", 0.0)

## Rarity and Type Rolling
static func _roll_rarity() -> GameEnums.ItemRarity:
	var roll = rng.randf() * 100.0
	var cumulative = 0.0
	
	for rarity in GameEnums.ItemRarity.values():
		cumulative += GameConstants.get_rarity_threshold(rarity)
		if roll <= cumulative:
			return rarity
	
	return GameEnums.ItemRarity.COMMON

static func _roll_loot_rarity(enemy_type: GameEnums.EnemyType) -> GameEnums.ItemRarity:
	var base_rarity = _roll_rarity()
	
	# Increase rarity chance for special enemies
	match enemy_type:
		GameEnums.EnemyType.ELITE:
			if base_rarity < GameEnums.ItemRarity.RARE:
				base_rarity += 1
		GameEnums.EnemyType.BOSS:
			if base_rarity < GameEnums.ItemRarity.EPIC:
				base_rarity += 2
		GameEnums.EnemyType.WORLD_BOSS:
			if base_rarity < GameEnums.ItemRarity.LEGENDARY:
				base_rarity += 3
	
	return min(base_rarity, GameEnums.ItemRarity.DIVINE)

static func _roll_item_type() -> GameEnums.ItemType:
	var total_weight = 0
	for weight in type_weights.values():
		total_weight += weight
	
	var roll = rng.randi() % total_weight
	var cumulative = 0
	
	for item_type in type_weights:
		cumulative += type_weights[item_type]
		if roll < cumulative:
			return item_type
	
	return GameEnums.ItemType.WEAPON

## Stat Scaling and Generation
static func _scale_stats_for_rarity(base_stats: Dictionary, rarity: GameEnums.ItemRarity, level: int) -> Dictionary:
	var scaled_stats = {}
	var range_data = stat_ranges[rarity]
	var level_multiplier = 1.0 + (level - 1) * 0.1  # 10% increase per level
	
	for stat in base_stats:
		var base_value = base_stats[stat]
		var min_roll = range_data.min
		var max_roll = range_data.max
		var roll_multiplier = rng.randf_range(min_roll, max_roll) / 10.0
		
		scaled_stats[stat] = int(base_value * (1.0 + roll_multiplier) * level_multiplier)
	
	# Add random additional stats for higher rarities
	if rarity >= GameEnums.ItemRarity.RARE:
		_add_random_stats(scaled_stats, rarity)
	
	return scaled_stats

static func _add_random_stats(stats: Dictionary, rarity: GameEnums.ItemRarity) -> void:
	var additional_stats_count = 0
	
	match rarity:
		GameEnums.ItemRarity.RARE:
			additional_stats_count = 1
		GameEnums.ItemRarity.EPIC:
			additional_stats_count = 2
		GameEnums.ItemRarity.LEGENDARY:
			additional_stats_count = 3
		GameEnums.ItemRarity.MYTHIC:
			additional_stats_count = 4
		GameEnums.ItemRarity.DIVINE:
			additional_stats_count = 5
	
	var available_stats = []
	for stat in GameEnums.StatType.values():
		if not stats.has(stat):
			available_stats.append(stat)
	
	for i in min(additional_stats_count, available_stats.size()):
		var random_stat = available_stats[rng.randi() % available_stats.size()]
		available_stats.erase(random_stat)
		
		var range_data = stat_ranges[rarity]
		var stat_value = rng.randi_range(range_data.min, range_data.max)
		stats[random_stat] = stat_value

## Utility Functions
static func _generate_item_id(template: Dictionary, rarity: GameEnums.ItemRarity) -> String:
	var base_name = template.get("name", "item").to_lower().replace(" ", "_")
	var rarity_suffix = GameEnums.ItemRarity.keys()[rarity].to_lower()
	var unique_id = rng.randi() % 10000
	return "%s_%s_%d" % [base_name, rarity_suffix, unique_id]

static func _generate_item_name(template: Dictionary, rarity: GameEnums.ItemRarity) -> String:
	var base_name = template.get("name", "Unknown Item")
	
	# Add rarity prefixes for higher rarities
	match rarity:
		GameEnums.ItemRarity.EPIC:
			return "Superior " + base_name
		GameEnums.ItemRarity.LEGENDARY:
			return "Legendary " + base_name
		GameEnums.ItemRarity.MYTHIC:
			return "Mythic " + base_name
		GameEnums.ItemRarity.DIVINE:
			return "Divine " + base_name
		_:
			return base_name

static func _get_level_offset(rarity: GameEnums.ItemRarity) -> int:
	match rarity:
		GameEnums.ItemRarity.UNCOMMON:
			return 1
		GameEnums.ItemRarity.RARE:
			return 3
		GameEnums.ItemRarity.EPIC:
			return 6
		GameEnums.ItemRarity.LEGENDARY:
			return 10
		GameEnums.ItemRarity.MYTHIC:
			return 15
		GameEnums.ItemRarity.DIVINE:
			return 25
		_:
			return 0

static func _calculate_sell_value(rarity: GameEnums.ItemRarity, level: int) -> int:
	var base_value = 10
	var rarity_multiplier = pow(2, rarity)
	var level_multiplier = 1.0 + (level - 1) * 0.2
	
	return int(base_value * rarity_multiplier * level_multiplier)

static func _get_random_template_for_type(item_type: GameEnums.ItemType) -> String:
	var matching_templates = []
	
	for template_id in item_templates:
		var template = item_templates[template_id]
		if template.get("item_type") == item_type:
			matching_templates.append(template_id)
	
	if matching_templates.is_empty():
		return ""
	
	return matching_templates[rng.randi() % matching_templates.size()]

static func _apply_boss_loot_bonus(item: Item) -> void:
	# Increase all stats by 25%
	for stat in item.stat_modifiers:
		item.stat_modifiers[stat] = int(item.stat_modifiers[stat] * 1.25)

static func _apply_elite_loot_bonus(item: Item) -> void:
	# Increase all stats by 15%
	for stat in item.stat_modifiers:
		item.stat_modifiers[stat] = int(item.stat_modifiers[stat] * 1.15)
