class_name GameConstants
extends RefCounted

## Global constants and configuration values
## Centralized location for game balance and system parameters

# Game Version and Build Info
const GAME_VERSION: String = "0.1.0"
const BUILD_NUMBER: int = 1
const MIN_GODOT_VERSION: String = "4.4"

# Performance and Optimization
const MAX_ENEMIES_PER_WAVE: int = 50
const MAX_PARTICLES_ON_SCREEN: int = 100
const MAX_DAMAGE_NUMBERS: int = 20
const OBJECT_POOL_SIZE: int = 50
const MAX_LOOT_DROPS_PER_WAVE: int = 15

# Player Base Stats
const PLAYER_BASE_HEALTH: int = 100
const PLAYER_BASE_MANA: int = 50
const PLAYER_BASE_ATTACK: int = 10
const PLAYER_BASE_DEFENSE: int = 5
const PLAYER_BASE_MOVE_SPEED: float = 5.0
const PLAYER_BASE_ATTACK_SPEED: float = 1.0
const PLAYER_BASE_CRIT_CHANCE: float = 0.05
const PLAYER_BASE_CRIT_DAMAGE: float = 1.5

# Level and Experience System
const MAX_PLAYER_LEVEL: int = 100
const BASE_EXPERIENCE_REQUIRED: int = 100
const EXPERIENCE_SCALING_FACTOR: float = 1.15
const SKILL_POINTS_PER_LEVEL: int = 1
const STAT_POINTS_PER_LEVEL: int = 2

# Combat System
const AUTO_ATTACK_RANGE: float = 3.0
const SKILL_RANGE_MULTIPLIER: float = 1.5
const DAMAGE_VARIANCE: float = 0.1  # ±10% damage variance
const CRITICAL_HIT_MULTIPLIER: float = 2.0
const EXCELLENT_HIT_MULTIPLIER: float = 1.5
const DOUBLE_HIT_CHANCE: float = 0.02
const MISS_CHANCE_BASE: float = 0.05

# Status Effects Duration (in seconds)
const POISON_DURATION: float = 5.0
const BURN_DURATION: float = 3.0
const FREEZE_DURATION: float = 2.0
const STUN_DURATION: float = 1.5
const SLOW_DURATION: float = 4.0
const HASTE_DURATION: float = 6.0
const SHIELD_DURATION: float = 10.0
const REGENERATION_DURATION: float = 8.0

# Item System
const MAX_INVENTORY_SIZE: int = 100
const MAX_SOCKETS_PER_ITEM: int = 3
const SOCKET_UNLOCK_LEVELS: Array[int] = [1, 10, 25]
const GEAR_DURABILITY_MAX: int = 100
const REPAIR_COST_MULTIPLIER: float = 0.1

# Rarity Drop Rates (percentages)
const DROP_RATE_COMMON: float = 60.0
const DROP_RATE_UNCOMMON: float = 25.0
const DROP_RATE_RARE: float = 10.0
const DROP_RATE_EPIC: float = 3.5
const DROP_RATE_LEGENDARY: float = 1.0
const DROP_RATE_MYTHIC: float = 0.4
const DROP_RATE_DIVINE: float = 0.1

# Currency and Economy
const STARTING_GOLD: int = 100
const STARTING_GEMS: int = 0
const STARTING_RUNE_POWDER: int = 0
const GOLD_PER_ENEMY_BASE: int = 5
const EXPERIENCE_PER_ENEMY_BASE: int = 10
const SHOP_REFRESH_COST: int = 10
const ITEM_SELL_VALUE_MULTIPLIER: float = 0.25

# Wave and Run System
const WAVES_PER_RUN: int = 20
const ENEMIES_PER_WAVE_BASE: int = 5
const WAVE_SCALING_FACTOR: float = 1.2
const BOSS_WAVE_INTERVAL: int = 5
const ELITE_SPAWN_CHANCE: float = 0.15
const WAVE_COMPLETION_BONUS: int = 50

# Biome Configuration
const BIOME_UNLOCK_LEVELS: Dictionary = {
	GameEnums.BiomeType.FOREST: 1,
	GameEnums.BiomeType.DESERT: 10,
	GameEnums.BiomeType.ICE: 20,
	GameEnums.BiomeType.VOLCANO: 35,
	GameEnums.BiomeType.SHADOW: 50,
	GameEnums.BiomeType.CELESTIAL: 75
}

# Skill System
const MAX_SKILL_LEVEL: int = 10
const PASSIVE_SKILL_POINTS_REQUIRED: int = 1
const ACTIVE_SKILL_COOLDOWN_BASE: float = 5.0
const SKILL_DAMAGE_SCALING: float = 1.2
const MAX_ACTIVE_SKILLS: int = 4

# Wings System
const FLIGHT_DURATION_BASE: float = 3.0
const FLIGHT_COOLDOWN: float = 10.0
const WING_SPEED_MULTIPLIER: float = 1.5
const WING_SOCKET_COUNT: int = 2

# UI and Mobile Optimization
const UI_SCALE_MOBILE: float = 1.2
const TOUCH_DEADZONE_RADIUS: float = 50.0
const VIRTUAL_JOYSTICK_SIZE: float = 150.0
const BUTTON_MIN_SIZE: float = 80.0
const NOTIFICATION_DURATION: float = 3.0
const DAMAGE_NUMBER_LIFETIME: float = 2.0

# Audio System
const MASTER_VOLUME_DEFAULT: float = 1.0
const SFX_VOLUME_DEFAULT: float = 0.8
const MUSIC_VOLUME_DEFAULT: float = 0.6
const MAX_SIMULTANEOUS_SOUNDS: int = 32
const AUDIO_FADE_DURATION: float = 1.0

# Visual Effects
const PARTICLE_LIFETIME_MAX: float = 5.0
const SCREEN_SHAKE_INTENSITY_MAX: float = 10.0
const HIT_FLASH_DURATION: float = 0.1
const LOOT_GLOW_INTENSITY: float = 1.5
const CRITICAL_HIT_SCALE: float = 1.5

# Networking and Multiplayer
const MAX_PLAYERS_IN_HUB: int = 50
const CHAT_MESSAGE_MAX_LENGTH: int = 100
const PLAYER_SYNC_RATE: float = 0.1  # 10 times per second
const CONNECTION_TIMEOUT: float = 30.0
const PING_INTERVAL: float = 5.0

# Save System
const AUTO_SAVE_INTERVAL: float = 30.0
const MAX_SAVE_BACKUPS: int = 3
const SAVE_COMPRESSION_ENABLED: bool = true
const CLOUD_SYNC_ENABLED: bool = false  # Future feature

# Performance Thresholds
const TARGET_FPS: int = 60
const MIN_FPS_THRESHOLD: int = 30
const PERFORMANCE_CHECK_INTERVAL: float = 5.0
const AUTO_QUALITY_ADJUSTMENT: bool = true

# Debug and Development
const DEBUG_MODE_ENABLED: bool = false
const SHOW_FPS_COUNTER: bool = false
const ENABLE_CHEATS: bool = false
const LOG_LEVEL: int = 1  # 0=None, 1=Error, 2=Warning, 3=Info, 4=Debug

# File Paths
const SAVE_DIRECTORY: String = "user://saves/"
const SETTINGS_FILE: String = "user://settings.cfg"
const LOG_FILE: String = "user://game.log"
const SCREENSHOT_DIRECTORY: String = "user://screenshots/"

# Shader and Graphics
const SHADOW_QUALITY_LEVELS: Array[int] = [512, 1024, 2048, 4096]
const TEXTURE_QUALITY_LEVELS: Array[float] = [0.5, 0.75, 1.0, 1.25]
const PARTICLE_QUALITY_MULTIPLIERS: Array[float] = [0.25, 0.5, 1.0, 1.5]

# Mobile Specific
const BATTERY_OPTIMIZATION_ENABLED: bool = true
const THERMAL_THROTTLING_THRESHOLD: float = 0.8
const BACKGROUND_PROCESSING_REDUCED: bool = true
const HAPTIC_FEEDBACK_ENABLED: bool = true

# Achievement System
const ACHIEVEMENT_CATEGORIES: Array[String] = [
	"Combat Master",
	"Explorer",
	"Collector",
	"Survivor",
	"Social",
	"Special"
]

# Localization
const DEFAULT_LANGUAGE: String = "en"
const SUPPORTED_LANGUAGES: Array[String] = ["en", "es", "fr", "de", "ja", "ko", "zh"]

# Analytics and Telemetry
const ANALYTICS_ENABLED: bool = false
const CRASH_REPORTING_ENABLED: bool = false
const USAGE_STATISTICS_ENABLED: bool = false

# Helper Functions for Constants
static func get_experience_required_for_level(level: int) -> int:
	if level <= 1:
		return 0
	return int(BASE_EXPERIENCE_REQUIRED * pow(EXPERIENCE_SCALING_FACTOR, level - 1))

static func get_total_experience_for_level(level: int) -> int:
	var total = 0
	for i in range(2, level + 1):
		total += get_experience_required_for_level(i)
	return total

static func get_enemies_for_wave(wave: int) -> int:
	return max(1, int(ENEMIES_PER_WAVE_BASE * pow(WAVE_SCALING_FACTOR, wave - 1)))

static func get_gold_reward_for_wave(wave: int) -> int:
	return int(WAVE_COMPLETION_BONUS * wave * 0.5)

static func get_rarity_threshold(rarity: GameEnums.ItemRarity) -> float:
	match rarity:
		GameEnums.ItemRarity.COMMON:
			return DROP_RATE_COMMON
		GameEnums.ItemRarity.UNCOMMON:
			return DROP_RATE_UNCOMMON
		GameEnums.ItemRarity.RARE:
			return DROP_RATE_RARE
		GameEnums.ItemRarity.EPIC:
			return DROP_RATE_EPIC
		GameEnums.ItemRarity.LEGENDARY:
			return DROP_RATE_LEGENDARY
		GameEnums.ItemRarity.MYTHIC:
			return DROP_RATE_MYTHIC
		GameEnums.ItemRarity.DIVINE:
			return DROP_RATE_DIVINE
		_:
			return 0.0

static func is_boss_wave(wave: int) -> bool:
	return wave % BOSS_WAVE_INTERVAL == 0

static func get_biome_unlock_level(biome: GameEnums.BiomeType) -> int:
	return BIOME_UNLOCK_LEVELS.get(biome, 999)
