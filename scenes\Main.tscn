[gd_scene load_steps=17 format=3 uid="uid://bqxvnhqxqxqxq"]

[ext_resource type="Script" uid="uid://dxb2maia2k4jo" path="res://scenes/Main.gd" id="1"]
[ext_resource type="Script" uid="uid://cp1h3u844g48j" path="res://player/Player.gd" id="2"]
[ext_resource type="Script" uid="uid://dxu1pxayow00" path="res://player/PlayerStats.gd" id="3"]
[ext_resource type="Script" uid="uid://co5ymuybkqrs0" path="res://player/PlayerInput.gd" id="4"]
[ext_resource type="PackedScene" uid="uid://bqxvnhqxqxqxr" path="res://ui/HUD.tscn" id="5"]
[ext_resource type="Script" uid="uid://b72lpwknwj6rw" path="res://enemies/EnemySpawner.gd" id="6"]
[ext_resource type="Script" uid="uid://d0hi3pa0mulva" path="res://loot/LootManager.gd" id="7"]
[ext_resource type="Script" uid="uid://b8hxt2rf5d21e" path="res://levels/WaveManager.gd" id="8"]
[ext_resource type="Script" uid="uid://ckjqhpuj30766" path="res://skills/SkillTree.gd" id="9"]
[ext_resource type="Script" uid="uid://bteaukavrq8og" path="res://vfx/EffectManager.gd" id="10"]
[ext_resource type="Script" uid="uid://pkiitxqaxnb2" path="res://audio/AudioManager.gd" id="11"]
[ext_resource type="Script" uid="uid://bv6lk6igmbe8u" path="res://environments/BiomeManager.gd" id="12"]

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(1, 0.1, 1)

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(1, 0.1, 1)

[sub_resource type="CapsuleMesh" id="CapsuleMesh_1"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_1"]

[node name="Main" type="Node3D"]
script = ExtResource("1")

[node name="Environment" type="Node3D" parent="."]

[node name="DirectionalLight3D" type="DirectionalLight3D" parent="Environment"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
shadow_enabled = true

[node name="Ground" type="StaticBody3D" parent="Environment"]
collision_layer = 4
collision_mask = 0

[node name="GroundMesh" type="MeshInstance3D" parent="Environment/Ground"]
transform = Transform3D(20, 0, 0, 0, 1, 0, 0, 0, 20, 0, 0, 0)
mesh = SubResource("BoxMesh_1")

[node name="GroundCollision" type="CollisionShape3D" parent="Environment/Ground"]
transform = Transform3D(20, 0, 0, 0, 1, 0, 0, 0, 20, 0, 0, 0)
shape = SubResource("BoxShape3D_1")

[node name="Player" type="CharacterBody3D" parent="." groups=["player"]]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
collision_mask = 6
script = ExtResource("2")

[node name="PlayerMesh" type="MeshInstance3D" parent="Player"]
mesh = SubResource("CapsuleMesh_1")

[node name="PlayerCollision" type="CollisionShape3D" parent="Player"]
shape = SubResource("CapsuleShape3D_1")

[node name="PlayerStats" type="Node" parent="Player"]
script = ExtResource("3")

[node name="PlayerInput" type="Node" parent="Player"]
script = ExtResource("4")

[node name="CameraController" type="Node3D" parent="Player"]

[node name="CameraPivot" type="Node3D" parent="Player/CameraController"]
transform = Transform3D(1, 0, 0, 0, 0.707107, 0.707107, 0, -0.707107, 0.707107, 0, 5, 5)

[node name="Camera3D" type="Camera3D" parent="Player/CameraController/CameraPivot"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 8)
fov = 60.0

[node name="GameSystems" type="Node" parent="."]

[node name="EnemySpawner" type="Node" parent="GameSystems"]
script = ExtResource("6")

[node name="LootManager" type="Node" parent="GameSystems"]
script = ExtResource("7")

[node name="WaveManager" type="Node" parent="GameSystems"]
script = ExtResource("8")

[node name="SkillTree" type="Node" parent="GameSystems"]
script = ExtResource("9")

[node name="EffectManager" type="Node" parent="GameSystems"]
script = ExtResource("10")

[node name="AudioManager" type="Node" parent="GameSystems"]
script = ExtResource("11")

[node name="BiomeManager" type="Node" parent="GameSystems"]
script = ExtResource("12")

[node name="UI" type="CanvasLayer" parent="."]

[node name="HUD" parent="UI" instance=ExtResource("5")]
grow_horizontal = 2
grow_vertical = 2
