class_name PlayerStats
extends Node

## Player statistics management system with modifier stacking
## Implements the Observer pattern for stat change notifications

signal stat_changed(stat_type: GameEnums.StatType, old_value: float, new_value: float)
signal health_changed(current: int, max: int)
signal mana_changed(current: int, max: int)
signal level_up(new_level: int)

# Base stats (permanent character progression)
var base_stats: Dictionary = {}

# Equipment modifiers (from gear, weapons, etc.)
var equipment_modifiers: Dictionary = {}

# Temporary modifiers (buffs, debuffs, skills)
var temporary_modifiers: Array[Dictionary] = []

# Current values
var current_health: int = 100
var current_mana: int = 50
var current_level: int = 1
var current_experience: int = 0

# Cached calculated stats for performance
var cached_stats: Dictionary = {}
var stats_dirty: bool = true

func _ready() -> void:
	_initialize_base_stats()
	_connect_events()
	_calculate_all_stats()

func _initialize_base_stats() -> void:
	base_stats = {
		GameEnums.StatType.ATTACK: GameConstants.PLAYER_BASE_ATTACK,
		GameEnums.StatType.DEFENSE: GameConstants.PLAYER_BASE_DEFENSE,
		GameEnums.StatType.HEALTH: GameConstants.PLAYER_BASE_HEALTH,
		GameEnums.StatType.MANA: GameConstants.PLAYER_BASE_MANA,
		GameEnums.StatType.CRIT_CHANCE: GameConstants.PLAYER_BASE_CRIT_CHANCE,
		GameEnums.StatType.CRIT_DAMAGE: GameConstants.PLAYER_BASE_CRIT_DAMAGE,
		GameEnums.StatType.ATTACK_SPEED: GameConstants.PLAYER_BASE_ATTACK_SPEED,
		GameEnums.StatType.MOVE_SPEED: GameConstants.PLAYER_BASE_MOVE_SPEED,
		GameEnums.StatType.EXPERIENCE_GAIN: 1.0,
		GameEnums.StatType.GOLD_FIND: 1.0,
		GameEnums.StatType.MAGIC_FIND: 1.0
	}
	
	# Initialize equipment modifiers
	for stat in GameEnums.StatType.values():
		equipment_modifiers[stat] = 0.0
	
	# Set initial health and mana
	current_health = get_max_health()
	current_mana = get_max_mana()

func _connect_events() -> void:
	EventBus.player_stats_changed.connect(_on_equipment_stats_changed)
	EventBus.experience_gained.connect(_on_experience_gained)
	EventBus.item_equipped.connect(_on_item_equipped)
	EventBus.item_unequipped.connect(_on_item_unequipped)

## Core Stat Calculation
func get_stat(stat_type: GameEnums.StatType) -> float:
	if stats_dirty:
		_calculate_all_stats()
	
	return cached_stats.get(stat_type, 0.0)

func _calculate_all_stats() -> void:
	cached_stats.clear()
	
	for stat in GameEnums.StatType.values():
		var total = base_stats.get(stat, 0.0)
		
		# Add equipment modifiers
		total += equipment_modifiers.get(stat, 0.0)
		
		# Add temporary modifiers
		for modifier in temporary_modifiers:
			if modifier.has(stat):
				if modifier.get("multiplicative", false):
					total *= (1.0 + modifier[stat])
				else:
					total += modifier[stat]
		
		cached_stats[stat] = total
	
	stats_dirty = false

func invalidate_stats() -> void:
	stats_dirty = true

## Stat Accessors
func get_max_health() -> int:
	return int(get_stat(GameEnums.StatType.HEALTH))

func get_max_mana() -> int:
	return int(get_stat(GameEnums.StatType.MANA))

func get_attack() -> int:
	return int(get_stat(GameEnums.StatType.ATTACK))

func get_defense() -> int:
	return int(get_stat(GameEnums.StatType.DEFENSE))

func get_crit_chance() -> float:
	return get_stat(GameEnums.StatType.CRIT_CHANCE)

func get_crit_damage() -> float:
	return get_stat(GameEnums.StatType.CRIT_DAMAGE)

func get_attack_speed() -> float:
	return get_stat(GameEnums.StatType.ATTACK_SPEED)

func get_move_speed() -> float:
	return get_stat(GameEnums.StatType.MOVE_SPEED)

func get_experience_multiplier() -> float:
	return get_stat(GameEnums.StatType.EXPERIENCE_GAIN)

func get_gold_find() -> float:
	return get_stat(GameEnums.StatType.GOLD_FIND)

func get_magic_find() -> float:
	return get_stat(GameEnums.StatType.MAGIC_FIND)

## Health and Mana Management
func take_damage(amount: int) -> bool:
	var old_health = current_health
	current_health = max(0, current_health - amount)
	
	health_changed.emit(current_health, get_max_health())
	EventBus.player_health_changed.emit(current_health, get_max_health())
	
	if current_health <= 0 and old_health > 0:
		EventBus.player_died.emit()
		return true  # Player died
	
	return false

func heal(amount: int) -> void:
	var old_health = current_health
	current_health = min(get_max_health(), current_health + amount)
	
	if current_health != old_health:
		health_changed.emit(current_health, get_max_health())
		EventBus.player_health_changed.emit(current_health, get_max_health())

func consume_mana(amount: int) -> bool:
	if current_mana >= amount:
		current_mana -= amount
		mana_changed.emit(current_mana, get_max_mana())
		return true
	return false

func restore_mana(amount: int) -> void:
	var old_mana = current_mana
	current_mana = min(get_max_mana(), current_mana + amount)
	
	if current_mana != old_mana:
		mana_changed.emit(current_mana, get_max_mana())

func get_health_percentage() -> float:
	var max_hp = get_max_health()
	return float(current_health) / float(max_hp) if max_hp > 0 else 0.0

func get_mana_percentage() -> float:
	var max_mp = get_max_mana()
	return float(current_mana) / float(max_mp) if max_mp > 0 else 0.0

## Level and Experience System
func add_experience(amount: int) -> void:
	var multiplied_exp = int(amount * get_experience_multiplier())
	current_experience += multiplied_exp
	
	EventBus.experience_gained.emit(multiplied_exp)
	_check_level_up()

func _check_level_up() -> void:
	var required_exp = GameConstants.get_experience_required_for_level(current_level + 1)
	
	while current_experience >= required_exp and current_level < GameConstants.MAX_PLAYER_LEVEL:
		current_experience -= required_exp
		current_level += 1
		
		_apply_level_up_bonuses()
		level_up.emit(current_level)
		EventBus.player_level_up.emit(current_level)
		
		required_exp = GameConstants.get_experience_required_for_level(current_level + 1)

func _apply_level_up_bonuses() -> void:
	# Increase base stats on level up
	base_stats[GameEnums.StatType.HEALTH] += GameConstants.STAT_POINTS_PER_LEVEL * 5
	base_stats[GameEnums.StatType.MANA] += GameConstants.STAT_POINTS_PER_LEVEL * 2
	base_stats[GameEnums.StatType.ATTACK] += GameConstants.STAT_POINTS_PER_LEVEL
	base_stats[GameEnums.StatType.DEFENSE] += GameConstants.STAT_POINTS_PER_LEVEL
	
	# Heal to full on level up
	current_health = get_max_health()
	current_mana = get_max_mana()
	
	invalidate_stats()
	
	# Award skill points
	EventBus.skill_point_spent.emit("", GameConstants.SKILL_POINTS_PER_LEVEL)

func get_experience_to_next_level() -> int:
	if current_level >= GameConstants.MAX_PLAYER_LEVEL:
		return 0
	return GameConstants.get_experience_required_for_level(current_level + 1)

func get_level_progress() -> float:
	var required = get_experience_to_next_level()
	if required <= 0:
		return 1.0
	return float(current_experience) / float(required)

## Modifier Management
func add_temporary_modifier(modifier: Dictionary, duration: float = 0.0) -> void:
	var mod_data = modifier.duplicate()
	mod_data["start_time"] = Time.get_time_dict_from_system()["unix"]
	mod_data["duration"] = duration
	mod_data["id"] = _generate_modifier_id()
	
	temporary_modifiers.append(mod_data)
	invalidate_stats()
	
	if duration > 0.0:
		get_tree().create_timer(duration).timeout.connect(_remove_modifier_by_id.bind(mod_data.id))

func remove_temporary_modifier(modifier_id: String) -> bool:
	for i in range(temporary_modifiers.size()):
		if temporary_modifiers[i].get("id") == modifier_id:
			temporary_modifiers.remove_at(i)
			invalidate_stats()
			return true
	return false

func _remove_modifier_by_id(modifier_id: String) -> void:
	remove_temporary_modifier(modifier_id)

func _generate_modifier_id() -> String:
	return "mod_%d_%d" % [Time.get_time_dict_from_system()["unix"], randi() % 10000]

func clear_temporary_modifiers() -> void:
	temporary_modifiers.clear()
	invalidate_stats()

## Equipment Integration
func _on_equipment_stats_changed(stat_changes: Dictionary) -> void:
	for stat in stat_changes:
		equipment_modifiers[stat] = equipment_modifiers.get(stat, 0.0) + stat_changes[stat]
	
	invalidate_stats()
	_update_current_health_mana()

func _on_item_equipped(item: Item, slot: String) -> void:
	var item_stats = item.get_total_stat_modifiers()
	for stat in item_stats:
		equipment_modifiers[stat] = equipment_modifiers.get(stat, 0.0) + item_stats[stat]
	
	invalidate_stats()
	_update_current_health_mana()

func _on_item_unequipped(item: Item, slot: String) -> void:
	var item_stats = item.get_total_stat_modifiers()
	for stat in item_stats:
		equipment_modifiers[stat] = equipment_modifiers.get(stat, 0.0) - item_stats[stat]
	
	invalidate_stats()
	_update_current_health_mana()

func _update_current_health_mana() -> void:
	# Adjust current health/mana if max values changed
	var new_max_health = get_max_health()
	var new_max_mana = get_max_mana()
	
	if current_health > new_max_health:
		current_health = new_max_health
		health_changed.emit(current_health, new_max_health)
	
	if current_mana > new_max_mana:
		current_mana = new_max_mana
		mana_changed.emit(current_mana, new_max_mana)

func _on_experience_gained(amount: int) -> void:
	# This is handled by add_experience, but we can add additional logic here
	pass

## Utility Functions
func get_all_stats() -> Dictionary:
	if stats_dirty:
		_calculate_all_stats()
	return cached_stats.duplicate()

func get_stat_summary() -> Dictionary:
	return {
		"level": current_level,
		"health": "%d/%d" % [current_health, get_max_health()],
		"mana": "%d/%d" % [current_mana, get_max_mana()],
		"attack": get_attack(),
		"defense": get_defense(),
		"crit_chance": "%.1f%%" % (get_crit_chance() * 100),
		"attack_speed": "%.1f" % get_attack_speed(),
		"move_speed": "%.1f" % get_move_speed()
	}

func reset_to_full() -> void:
	current_health = get_max_health()
	current_mana = get_max_mana()
	health_changed.emit(current_health, get_max_health())
	mana_changed.emit(current_mana, get_max_mana())

## Serialization
func to_dict() -> Dictionary:
	return {
		"base_stats": base_stats,
		"current_health": current_health,
		"current_mana": current_mana,
		"current_level": current_level,
		"current_experience": current_experience,
		"equipment_modifiers": equipment_modifiers
	}

func from_dict(data: Dictionary) -> void:
	base_stats = data.get("base_stats", {})
	current_health = data.get("current_health", 100)
	current_mana = data.get("current_mana", 50)
	current_level = data.get("current_level", 1)
	current_experience = data.get("current_experience", 0)
	equipment_modifiers = data.get("equipment_modifiers", {})
	
	invalidate_stats()
	_calculate_all_stats()
