class_name AudioManager
extends Node

## Audio system managing sound effects, music, and spatial audio
## Implements the Observer pattern for audio events and pooling for performance

signal music_track_changed(track_name: String)
signal sound_effect_played(sound_name: String, position: Vector3)
signal audio_settings_changed()

@export var master_volume: float = 1.0
@export var sfx_volume: float = 0.8
@export var music_volume: float = 0.6
@export var max_simultaneous_sounds: int = 32

# Audio pools and tracking
var audio_pools: Dictionary = {}
var active_audio_sources: Array[AudioStreamPlayer3D] = []
var music_player: AudioStreamPlayer
var ui_audio_player: AudioStreamPlayer

# Audio libraries
var sound_effects: Dictionary = {}
var music_tracks: Dictionary = {}
var current_music_track: String = ""
var music_fade_tween: Tween

# Spatial audio settings
var max_audio_distance: float = 50.0
var audio_falloff_curve: float = 2.0

func _ready() -> void:
	_setup_audio_system()
	_load_audio_libraries()
	_create_audio_pools()
	_connect_events()

func _setup_audio_system() -> void:
	# Create main music player
	music_player = AudioStreamPlayer.new()
	music_player.bus = "Music"
	add_child(music_player)
	
	# Create UI audio player
	ui_audio_player = AudioStreamPlayer.new()
	ui_audio_player.bus = "SFX"
	add_child(ui_audio_player)
	
	# Set up audio buses
	_setup_audio_buses()

func _setup_audio_buses() -> void:
	# Create audio buses for different audio types
	var master_bus = AudioServer.get_bus_index("Master")
	
	# Create SFX bus
	AudioServer.add_bus(1)
	AudioServer.set_bus_name(1, "SFX")
	AudioServer.set_bus_send(1, "Master")
	
	# Create Music bus
	AudioServer.add_bus(2)
	AudioServer.set_bus_name(2, "Music")
	AudioServer.set_bus_send(2, "Master")
	
	# Create UI bus
	AudioServer.add_bus(3)
	AudioServer.set_bus_name(3, "UI")
	AudioServer.set_bus_send(3, "Master")
	
	# Set initial volumes
	_update_audio_volumes()

func _load_audio_libraries() -> void:
	# Load sound effect library
	sound_effects = {
		# Combat sounds
		"hit": "res://audio/sfx/hit.ogg",
		"critical_hit": "res://audio/sfx/critical_hit.ogg",
		"enemy_death": "res://audio/sfx/enemy_death.ogg",
		"player_attack": "res://audio/sfx/player_attack.ogg",
		"player_hurt": "res://audio/sfx/player_hurt.ogg",
		
		# Movement sounds
		"footstep": "res://audio/sfx/footstep.ogg",
		"dash": "res://audio/sfx/dash.ogg",
		"flight_start": "res://audio/sfx/flight_start.ogg",
		"flight_end": "res://audio/sfx/flight_end.ogg",
		
		# UI sounds
		"ui_click": "res://audio/sfx/ui_click.ogg",
		"ui_hover": "res://audio/sfx/ui_hover.ogg",
		"notification": "res://audio/sfx/notification.ogg",
		"level_up": "res://audio/sfx/level_up.ogg",
		
		# Loot sounds
		"loot_pickup": "res://audio/sfx/loot_pickup.ogg",
		"gold_pickup": "res://audio/sfx/gold_pickup.ogg",
		"rare_loot": "res://audio/sfx/rare_loot.ogg",
		
		# Skill sounds
		"skill_cast": "res://audio/sfx/skill_cast.ogg",
		"skill_cooldown": "res://audio/sfx/skill_cooldown.ogg",
		"buff_applied": "res://audio/sfx/buff_applied.ogg",
		
		# Environmental sounds
		"ambient_forest": "res://audio/sfx/ambient_forest.ogg",
		"wind": "res://audio/sfx/wind.ogg",
		"fire": "res://audio/sfx/fire.ogg"
	}
	
	# Load music track library
	music_tracks = {
		"main_menu": "res://audio/music/main_menu.ogg",
		"forest_combat": "res://audio/music/forest_combat.ogg",
		"desert_combat": "res://audio/music/desert_combat.ogg",
		"ice_combat": "res://audio/music/ice_combat.ogg",
		"boss_fight": "res://audio/music/boss_fight.ogg",
		"final_boss": "res://audio/music/final_boss.ogg",
		"victory": "res://audio/music/victory.ogg",
		"defeat": "res://audio/music/defeat.ogg"
	}

func _create_audio_pools() -> void:
	# Create pools for frequently used sounds
	var pooled_sounds = ["hit", "footstep", "ui_click", "loot_pickup"]
	
	for sound_name in pooled_sounds:
		audio_pools[sound_name] = {
			"available": [],
			"in_use": []
		}
		
		# Pre-create audio sources
		for i in 5:
			var audio_source = _create_audio_source()
			audio_pools[sound_name].available.append(audio_source)

func _create_audio_source() -> AudioStreamPlayer3D:
	var audio_source = AudioStreamPlayer3D.new()
	audio_source.bus = "SFX"
	audio_source.max_distance = max_audio_distance
	audio_source.attenuation_filter_cutoff_hz = 5000.0
	audio_source.attenuation_filter_db = -24.0
	add_child(audio_source)
	return audio_source

func _connect_events() -> void:
	EventBus.sound_requested.connect(_on_sound_requested)
	EventBus.music_changed.connect(_on_music_change_requested)
	EventBus.damage_dealt.connect(_on_damage_dealt)
	EventBus.item_picked_up.connect(_on_item_picked_up)
	EventBus.player_level_up.connect(_on_player_level_up)
	EventBus.skill_used.connect(_on_skill_used)

## Sound Effect System
func _on_sound_requested(sound_name: String, position: Vector3 = Vector3.ZERO) -> void:
	play_sound_effect(sound_name, position)

func play_sound_effect(sound_name: String, position: Vector3 = Vector3.ZERO, volume_db: float = 0.0) -> AudioStreamPlayer3D:
	if not sound_effects.has(sound_name):
		print("Unknown sound effect: %s" % sound_name)
		return null
	
	var audio_source = _get_audio_source(sound_name)
	if not audio_source:
		return null
	
	# Load and configure audio
	var audio_stream = _load_audio_stream(sound_effects[sound_name])
	if not audio_stream:
		return null
	
	audio_source.stream = audio_stream
	audio_source.global_position = position
	audio_source.volume_db = volume_db
	
	# Play sound
	audio_source.play()
	
	# Track active source
	active_audio_sources.append(audio_source)
	
	# Set up cleanup
	audio_source.finished.connect(_on_audio_finished.bind(audio_source, sound_name))
	
	sound_effect_played.emit(sound_name, position)
	return audio_source

func play_ui_sound(sound_name: String, volume_db: float = 0.0) -> void:
	if not sound_effects.has(sound_name):
		return
	
	var audio_stream = _load_audio_stream(sound_effects[sound_name])
	if not audio_stream:
		return
	
	ui_audio_player.stream = audio_stream
	ui_audio_player.volume_db = volume_db
	ui_audio_player.play()

func _get_audio_source(sound_name: String) -> AudioStreamPlayer3D:
	# Try to get from pool
	if audio_pools.has(sound_name):
		var pool = audio_pools[sound_name]
		if pool.available.size() > 0:
			var source = pool.available.pop_back()
			pool.in_use.append(source)
			return source
	
	# Check if we've hit the limit
	if active_audio_sources.size() >= max_simultaneous_sounds:
		_stop_oldest_sound()
	
	# Create new source
	return _create_audio_source()

func _load_audio_stream(path: String) -> AudioStream:
	# Use ResourceManager for caching
	return ResourceManager.get_resource(path)

## Music System
func _on_music_change_requested(track_name: String) -> void:
	change_music_track(track_name)

func change_music_track(track_name: String, fade_duration: float = 1.0) -> void:
	if track_name == current_music_track:
		return
	
	if not music_tracks.has(track_name):
		print("Unknown music track: %s" % track_name)
		return
	
	var new_track = _load_audio_stream(music_tracks[track_name])
	if not new_track:
		return
	
	# Fade out current track and fade in new track
	if music_fade_tween:
		music_fade_tween.kill()
	
	music_fade_tween = create_tween()
	
	if music_player.playing:
		# Fade out current track
		music_fade_tween.tween_property(music_player, "volume_db", -80.0, fade_duration * 0.5)
		music_fade_tween.tween_callback(_switch_music_track.bind(new_track))
		music_fade_tween.tween_property(music_player, "volume_db", _get_music_volume_db(), fade_duration * 0.5)
	else:
		# Just start new track
		_switch_music_track(new_track)
		music_player.volume_db = -80.0
		music_fade_tween.tween_property(music_player, "volume_db", _get_music_volume_db(), fade_duration)
	
	current_music_track = track_name
	music_track_changed.emit(track_name)

func _switch_music_track(new_track: AudioStream) -> void:
	music_player.stream = new_track
	music_player.play()

func stop_music(fade_duration: float = 1.0) -> void:
	if not music_player.playing:
		return
	
	if music_fade_tween:
		music_fade_tween.kill()
	
	music_fade_tween = create_tween()
	music_fade_tween.tween_property(music_player, "volume_db", -80.0, fade_duration)
	music_fade_tween.tween_callback(music_player.stop)
	
	current_music_track = ""

func pause_music() -> void:
	music_player.stream_paused = true

func resume_music() -> void:
	music_player.stream_paused = false

## Volume Control
func set_master_volume(volume: float) -> void:
	master_volume = clamp(volume, 0.0, 1.0)
	_update_audio_volumes()

func set_sfx_volume(volume: float) -> void:
	sfx_volume = clamp(volume, 0.0, 1.0)
	_update_audio_volumes()

func set_music_volume(volume: float) -> void:
	music_volume = clamp(volume, 0.0, 1.0)
	_update_audio_volumes()

func _update_audio_volumes() -> void:
	# Update bus volumes
	var master_bus = AudioServer.get_bus_index("Master")
	var sfx_bus = AudioServer.get_bus_index("SFX")
	var music_bus = AudioServer.get_bus_index("Music")
	var ui_bus = AudioServer.get_bus_index("UI")
	
	AudioServer.set_bus_volume_db(master_bus, linear_to_db(master_volume))
	AudioServer.set_bus_volume_db(sfx_bus, linear_to_db(sfx_volume))
	AudioServer.set_bus_volume_db(music_bus, linear_to_db(music_volume))
	AudioServer.set_bus_volume_db(ui_bus, linear_to_db(sfx_volume))
	
	audio_settings_changed.emit()

func _get_music_volume_db() -> float:
	return linear_to_db(music_volume)

## Audio Source Management
func _on_audio_finished(audio_source: AudioStreamPlayer3D, sound_name: String) -> void:
	# Remove from active list
	var index = active_audio_sources.find(audio_source)
	if index != -1:
		active_audio_sources.remove_at(index)
	
	# Return to pool if pooled
	if audio_pools.has(sound_name):
		var pool = audio_pools[sound_name]
		var pool_index = pool.in_use.find(audio_source)
		if pool_index != -1:
			pool.in_use.remove_at(pool_index)
			pool.available.append(audio_source)
			return
	
	# Otherwise remove the source
	audio_source.queue_free()

func _stop_oldest_sound() -> void:
	if active_audio_sources.size() > 0:
		var oldest_source = active_audio_sources[0]
		oldest_source.stop()

## Event Handlers
func _on_damage_dealt(target: Node, damage: int, damage_type: String) -> void:
	var sound_name = "hit"
	
	# Choose sound based on damage amount
	if damage >= 50:
		sound_name = "critical_hit"
	
	if target and target.has_method("get_global_position"):
		play_sound_effect(sound_name, target.global_position)
	else:
		play_sound_effect(sound_name)

func _on_item_picked_up(item: Item) -> void:
	var sound_name = "loot_pickup"
	
	# Special sound for rare items
	if item.rarity >= GameEnums.ItemRarity.RARE:
		sound_name = "rare_loot"
	elif item.item_type == GameEnums.ItemType.CURRENCY:
		sound_name = "gold_pickup"
	
	play_sound_effect(sound_name)

func _on_player_level_up(new_level: int) -> void:
	play_sound_effect("level_up")

func _on_skill_used(skill_name: String, cooldown: float) -> void:
	play_sound_effect("skill_cast")

## Biome Audio
func set_biome_audio(biome: GameEnums.BiomeType) -> void:
	var music_track = ""
	
	match biome:
		GameEnums.BiomeType.FOREST:
			music_track = "forest_combat"
		GameEnums.BiomeType.DESERT:
			music_track = "desert_combat"
		GameEnums.BiomeType.ICE:
			music_track = "ice_combat"
		_:
			music_track = "forest_combat"
	
	change_music_track(music_track)

## Utility Functions
func is_music_playing() -> bool:
	return music_player.playing

func get_current_music_track() -> String:
	return current_music_track

func get_active_sound_count() -> int:
	return active_audio_sources.size()

func mute_all_audio() -> void:
	set_master_volume(0.0)

func unmute_all_audio() -> void:
	set_master_volume(1.0)

## Cleanup
func stop_all_sounds() -> void:
	for audio_source in active_audio_sources:
		if is_instance_valid(audio_source):
			audio_source.stop()
	active_audio_sources.clear()

func cleanup_finished_sounds() -> void:
	for i in range(active_audio_sources.size() - 1, -1, -1):
		var audio_source = active_audio_sources[i]
		if not is_instance_valid(audio_source) or not audio_source.playing:
			active_audio_sources.remove_at(i)

## Debug Functions
func debug_play_all_sounds() -> void:
	var test_position = Vector3.ZERO
	var offset = 0
	
	for sound_name in sound_effects:
		play_sound_effect(sound_name, test_position + Vector3(offset * 2, 0, 0))
		offset += 1
		await get_tree().create_timer(0.5).timeout

func debug_print_audio_state() -> void:
	print("=== Audio Manager Debug ===")
	print("Master Volume: %.2f" % master_volume)
	print("SFX Volume: %.2f" % sfx_volume)
	print("Music Volume: %.2f" % music_volume)
	print("Current Music: %s" % current_music_track)
	print("Active Sounds: %d/%d" % [active_audio_sources.size(), max_simultaneous_sounds])
	
	for pool_name in audio_pools:
		var pool = audio_pools[pool_name]
		print("Pool %s: %d available, %d in use" % [pool_name, pool.available.size(), pool.in_use.size()])
	print("============================")
