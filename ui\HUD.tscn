[gd_scene load_steps=3 format=3 uid="uid://bqxvnhqxqxqxr"]

[ext_resource type="Script" path="res://ui/HUD.gd" id="1"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.5)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="HUD" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
script = ExtResource("1")

[node name="TopPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 100.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="HealthBar" type="ProgressBar" parent="TopPanel"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 20.0
offset_top = -15.0
offset_right = 220.0
offset_bottom = 15.0
max_value = 100.0
value = 100.0
show_percentage = false

[node name="HealthLabel" type="Label" parent="TopPanel/HealthBar"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -12.0
offset_right = 50.0
offset_bottom = 12.0
text = "100/100"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ManaBar" type="ProgressBar" parent="TopPanel"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 240.0
offset_top = -15.0
offset_right = 440.0
offset_bottom = 15.0
max_value = 100.0
value = 50.0
show_percentage = false

[node name="ManaLabel" type="Label" parent="TopPanel/ManaBar"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -12.0
offset_right = 50.0
offset_bottom = 12.0
text = "50/50"
horizontal_alignment = 1
vertical_alignment = 1

[node name="LevelLabel" type="Label" parent="TopPanel"]
layout_mode = 1
anchors_preset = 6
anchor_left = 1.0
anchor_top = 0.5
anchor_right = 1.0
anchor_bottom = 0.5
offset_left = -100.0
offset_top = -12.0
offset_right = -20.0
offset_bottom = 12.0
text = "Level 1"
horizontal_alignment = 1
vertical_alignment = 1

[node name="WaveInfo" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -200.0
offset_top = 120.0
offset_right = -20.0
offset_bottom = 200.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="WaveLabel" type="Label" parent="WaveInfo"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -80.0
offset_top = -20.0
offset_right = 80.0
offset_bottom = 20.0
text = "Wave 1/20"
horizontal_alignment = 1
vertical_alignment = 1

[node name="SkillButtons" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -320.0
offset_top = -120.0
offset_right = -20.0
offset_bottom = -20.0
alignment = 2

[node name="Skill1Button" type="Button" parent="SkillButtons"]
layout_mode = 2
custom_minimum_size = Vector2(80, 80)
text = "1"

[node name="Skill2Button" type="Button" parent="SkillButtons"]
layout_mode = 2
custom_minimum_size = Vector2(80, 80)
text = "2"

[node name="DashButton" type="Button" parent="SkillButtons"]
layout_mode = 2
custom_minimum_size = Vector2(80, 80)
text = "D"

[node name="VirtualJoystick" type="Control" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -200.0
offset_right = 200.0
offset_bottom = -20.0
mouse_filter = 2

[node name="JoystickBase" type="Panel" parent="VirtualJoystick"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -75.0
offset_top = -75.0
offset_right = 75.0
offset_bottom = 75.0
mouse_filter = 2

[node name="JoystickKnob" type="Panel" parent="VirtualJoystick"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -25.0
offset_top = -25.0
offset_right = 25.0
offset_bottom = 25.0
mouse_filter = 2

[node name="NotificationArea" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -200.0
offset_top = 120.0
offset_right = 200.0
offset_bottom = 220.0
alignment = 1
