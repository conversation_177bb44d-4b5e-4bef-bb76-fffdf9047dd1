extends Node

## Visual effects system managing particles, shaders, and screen effects
## Implements the Factory pattern for effect creation and pooling

signal effect_created(effect_name: String, position: Vector3)
signal screen_effect_triggered(effect_type: String, intensity: float)

@export var max_active_effects: int = 50
@export var effect_quality: float = 1.0  # 0.25 to 1.5 for performance scaling

# Effect pools and tracking
var active_effects: Array[Node] = []
var effect_pools: Dictionary = {}
var effect_templates: Dictionary = {}

# Screen effects
var screen_shake_intensity: float = 0.0
var screen_shake_duration: float = 0.0
var screen_shake_timer: float = 0.0

# References
var main_scene: Node3D
var camera: Camera3D

func _ready() -> void:
	_setup_effect_templates()
	_initialize_effect_pools()
	_connect_events()

func initialize(scene: Node3D) -> void:
	main_scene = scene
	camera = _find_camera_in_scene(scene)

func _find_camera_in_scene(scene: Node3D) -> Camera3D:
	# Find the main camera
	var cameras = scene.find_children("*", "Camera3D")
	return cameras[0] if cameras.size() > 0 else null

func _setup_effect_templates() -> void:
	# Define effect templates with their properties
	effect_templates = {
		# Combat Effects
		"hit_impact": {
			"type": "particle",
			"duration": 1.0,
			"particle_count": 20,
			"color": Color.WHITE,
			"size": 0.5,
			"spread": 45.0
		},
		"critical_hit": {
			"type": "particle",
			"duration": 1.5,
			"particle_count": 30,
			"color": Color.YELLOW,
			"size": 0.8,
			"spread": 60.0
		},
		"enemy_death": {
			"type": "particle",
			"duration": 2.0,
			"particle_count": 50,
			"color": Color.RED,
			"size": 1.0,
			"spread": 90.0
		},
		
		# Player Effects
		"dash": {
			"type": "trail",
			"duration": 0.5,
			"color": Color.CYAN,
			"width": 1.0,
			"length": 3.0
		},
		"flight_start": {
			"type": "particle",
			"duration": 1.0,
			"particle_count": 40,
			"color": Color.WHITE,
			"size": 0.6,
			"spread": 180.0
		},
		"flight_end": {
			"type": "particle",
			"duration": 0.8,
			"particle_count": 25,
			"color": Color.GRAY,
			"size": 0.4,
			"spread": 90.0
		},
		
		# Skill Effects
		"twisting_slash": {
			"type": "area_effect",
			"duration": 1.2,
			"radius": 4.0,
			"color": Color.SILVER,
			"rotation_speed": 720.0
		},
		"mana_burst": {
			"type": "explosion",
			"duration": 1.0,
			"radius": 3.0,
			"color": Color.BLUE,
			"intensity": 1.5
		},
		"multi_shot": {
			"type": "projectile_burst",
			"duration": 0.8,
			"projectile_count": 5,
			"color": Color.BROWN,
			"speed": 15.0
		},
		
		# Environmental Effects
		"loot_collected": {
			"type": "sparkle",
			"duration": 0.8,
			"particle_count": 15,
			"color": Color.GOLD,
			"size": 0.3,
			"spread": 30.0
		},
		"loot_sparkle": {
			"type": "ambient_sparkle",
			"duration": -1,  # Continuous
			"particle_count": 5,
			"color": Color.YELLOW,
			"size": 0.2,
			"interval": 2.0
		},
		
		# UI Effects
		"button_press": {
			"type": "ui_flash",
			"duration": 0.2,
			"color": Color.WHITE,
			"intensity": 0.8
		},
		"level_up": {
			"type": "screen_flash",
			"duration": 1.0,
			"color": Color.GOLD,
			"intensity": 0.6
		}
	}

func _initialize_effect_pools() -> void:
	# Create object pools for frequently used effects
	var pooled_effects = ["hit_impact", "critical_hit", "loot_collected", "dash"]
	
	for effect_name in pooled_effects:
		effect_pools[effect_name] = {
			"available": [],
			"in_use": [],
			"template": effect_templates[effect_name]
		}
		
		# Pre-create some effect instances
		for i in 5:
			var effect = _create_effect_instance(effect_name)
			effect.visible = false
			effect_pools[effect_name].available.append(effect)

func _connect_events() -> void:
	EventBus.effect_requested.connect(_on_effect_requested)
	EventBus.screen_shake_requested.connect(_on_screen_shake_requested)
	EventBus.damage_dealt.connect(_on_damage_dealt)

func _process(delta: float) -> void:
	_update_screen_shake(delta)
	_cleanup_finished_effects()

## Effect Creation
func _on_effect_requested(effect_name: String, position: Vector3, target: Node = null) -> void:
	create_effect(effect_name, position, target)

func create_effect(effect_name: String, position: Vector3, target: Node = null) -> Node:
	if not effect_templates.has(effect_name):
		print("Unknown effect: %s" % effect_name)
		return null
	
	# Check effect limit
	if active_effects.size() >= max_active_effects:
		_remove_oldest_effect()
	
	var effect = _get_or_create_effect(effect_name)
	if not effect:
		return null
	
	# Configure effect
	_configure_effect(effect, effect_name, position, target)
	
	# Add to scene
	if main_scene:
		main_scene.add_child(effect)
	else:
		get_tree().current_scene.add_child(effect)
	
	# Track effect
	active_effects.append(effect)
	
	# Emit signal
	effect_created.emit(effect_name, position)
	
	return effect

func _get_or_create_effect(effect_name: String) -> Node:
	# Try to get from pool first
	if effect_pools.has(effect_name):
		var pool = effect_pools[effect_name]
		if pool.available.size() > 0:
			var effect = pool.available.pop_back()
			pool.in_use.append(effect)
			return effect
	
	# Create new effect
	return _create_effect_instance(effect_name)

func _create_effect_instance(effect_name: String) -> Node:
	var template = effect_templates[effect_name]
	
	match template.type:
		"particle":
			return _create_particle_effect(template)
		"trail":
			return _create_trail_effect(template)
		"area_effect":
			return _create_area_effect(template)
		"explosion":
			return _create_explosion_effect(template)
		"projectile_burst":
			return _create_projectile_burst_effect(template)
		"sparkle":
			return _create_sparkle_effect(template)
		"ambient_sparkle":
			return _create_ambient_sparkle_effect(template)
		_:
			return _create_generic_effect(template)

func _create_particle_effect(template: Dictionary) -> GPUParticles3D:
	var particles = GPUParticles3D.new()
	
	# Configure particle system
	particles.emitting = false
	particles.amount = int(template.particle_count * effect_quality)
	particles.lifetime = template.duration
	particles.one_shot = true
	
	# Create material
	var material = ParticleProcessMaterial.new()
	material.direction = Vector3(0, 1, 0)
	material.initial_velocity_min = 2.0
	material.initial_velocity_max = 5.0
	material.angular_velocity_min = -180.0
	material.angular_velocity_max = 180.0
	material.gravity = Vector3(0, -9.8, 0)
	material.scale_min = template.size * 0.5
	material.scale_max = template.size * 1.5
	
	# Set spread
	material.spread = template.spread
	
	particles.process_material = material
	
	# Create mesh
	var mesh = SphereMesh.new()
	mesh.radius = 0.1
	mesh.height = 0.2
	particles.draw_pass_1 = mesh
	
	return particles

func _create_trail_effect(template: Dictionary) -> Node3D:
	var trail = Node3D.new()
	
	# Create trail using Line3D or custom mesh
	var mesh_instance = MeshInstance3D.new()
	var trail_mesh = ArrayMesh.new()
	
	# Create simple trail geometry
	var vertices = PackedVector3Array()
	var colors = PackedColorArray()
	
	for i in 10:
		vertices.append(Vector3(i * 0.1, 0, 0))
		colors.append(template.color)
	
	var arrays = []
	arrays.resize(Mesh.ARRAY_MAX)
	arrays[Mesh.ARRAY_VERTEX] = vertices
	arrays[Mesh.ARRAY_COLOR] = colors
	
	trail_mesh.add_surface_from_arrays(Mesh.PRIMITIVE_LINE_STRIP, arrays)
	mesh_instance.mesh = trail_mesh
	
	trail.add_child(mesh_instance)
	return trail

func _create_area_effect(template: Dictionary) -> Node3D:
	var area_effect = Node3D.new()
	
	# Create expanding circle effect
	var mesh_instance = MeshInstance3D.new()
	var cylinder_mesh = CylinderMesh.new()
	cylinder_mesh.top_radius = 0.1
	cylinder_mesh.bottom_radius = 0.1
	cylinder_mesh.height = 0.1
	mesh_instance.mesh = cylinder_mesh
	
	# Create material
	var material = StandardMaterial3D.new()
	material.albedo_color = template.color
	material.emission = template.color * 0.5
	material.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
	mesh_instance.material_override = material
	
	area_effect.add_child(mesh_instance)
	
	# Animate expansion
	var tween = area_effect.create_tween()
	tween.parallel().tween_property(cylinder_mesh, "top_radius", template.radius, template.duration)
	tween.parallel().tween_property(cylinder_mesh, "bottom_radius", template.radius, template.duration)
	tween.parallel().tween_property(material, "albedo_color:a", 0.0, template.duration)
	
	return area_effect

func _create_explosion_effect(template: Dictionary) -> Node3D:
	var explosion = Node3D.new()
	
	# Create sphere that expands and fades
	var mesh_instance = MeshInstance3D.new()
	var sphere_mesh = SphereMesh.new()
	sphere_mesh.radius = 0.1
	mesh_instance.mesh = sphere_mesh
	
	var material = StandardMaterial3D.new()
	material.albedo_color = template.color
	material.emission = template.color * template.intensity
	material.transparency = BaseMaterial3D.TRANSPARENCY_ALPHA
	mesh_instance.material_override = material
	
	explosion.add_child(mesh_instance)
	
	# Animate explosion
	var tween = explosion.create_tween()
	tween.parallel().tween_property(sphere_mesh, "radius", template.radius, template.duration)
	tween.parallel().tween_property(material, "albedo_color:a", 0.0, template.duration)
	
	return explosion

func _create_projectile_burst_effect(template: Dictionary) -> Node3D:
	var burst = Node3D.new()
	
	# Create multiple projectiles
	for i in template.projectile_count:
		var projectile = _create_projectile()
		var angle = (i * TAU) / template.projectile_count
		var direction = Vector3(cos(angle), 0, sin(angle))
		
		burst.add_child(projectile)
		
		# Animate projectile movement
		var tween = projectile.create_tween()
		tween.tween_property(projectile, "position", direction * template.speed, template.duration)
	
	return burst

func _create_projectile() -> MeshInstance3D:
	var projectile = MeshInstance3D.new()
	var mesh = CapsuleMesh.new()
	mesh.radius = 0.05
	mesh.height = 0.2
	projectile.mesh = mesh
	
	var material = StandardMaterial3D.new()
	material.albedo_color = Color.BROWN
	material.emission = Color.BROWN * 0.3
	projectile.material_override = material
	
	return projectile

func _create_sparkle_effect(template: Dictionary) -> GPUParticles3D:
	var sparkles = _create_particle_effect(template)
	
	# Modify for sparkle behavior
	var material = sparkles.process_material as ParticleProcessMaterial
	material.gravity = Vector3.ZERO
	material.initial_velocity_min = 0.5
	material.initial_velocity_max = 1.5
	
	return sparkles

func _create_ambient_sparkle_effect(template: Dictionary) -> GPUParticles3D:
	var sparkles = _create_sparkle_effect(template)
	sparkles.one_shot = false
	sparkles.emitting = true
	return sparkles

func _create_generic_effect(template: Dictionary) -> Node3D:
	var effect = Node3D.new()
	
	var mesh_instance = MeshInstance3D.new()
	var sphere_mesh = SphereMesh.new()
	sphere_mesh.radius = 0.5
	mesh_instance.mesh = sphere_mesh
	
	var material = StandardMaterial3D.new()
	material.albedo_color = template.get("color", Color.WHITE)
	mesh_instance.material_override = material
	
	effect.add_child(mesh_instance)
	return effect

## Effect Configuration
func _configure_effect(effect: Node, effect_name: String, position: Vector3, target: Node) -> void:
	effect.global_position = position
	effect.visible = true
	
	var template = effect_templates[effect_name]
	
	# Start particle emission
	if effect is GPUParticles3D:
		effect.emitting = true
		effect.restart()
	
	# Set up auto-cleanup
	if template.duration > 0:
		get_tree().create_timer(template.duration).timeout.connect(
			func(): _cleanup_effect(effect, effect_name)
		)

## Screen Effects
func _on_screen_shake_requested(intensity: float, duration: float) -> void:
	screen_shake_intensity = intensity
	screen_shake_duration = duration
	screen_shake_timer = duration

func _update_screen_shake(delta: float) -> void:
	if screen_shake_timer <= 0:
		return
	
	screen_shake_timer -= delta
	
	if camera:
		var shake_amount = screen_shake_intensity * (screen_shake_timer / screen_shake_duration)
		var shake_offset = Vector3(
			randf_range(-shake_amount, shake_amount),
			randf_range(-shake_amount, shake_amount),
			0
		)
		
		# Apply shake to camera position
		camera.position += shake_offset * delta * 10.0

## Effect Management
func _cleanup_finished_effects() -> void:
	for i in range(active_effects.size() - 1, -1, -1):
		var effect = active_effects[i]
		if not is_instance_valid(effect):
			active_effects.remove_at(i)
			continue
		
		# Check if particle effect is finished
		if effect is GPUParticles3D and not effect.emitting:
			_cleanup_effect(effect, "")
			active_effects.remove_at(i)

func _cleanup_effect(effect: Node, effect_name: String) -> void:
	if not is_instance_valid(effect):
		return
	
	# Return to pool if pooled
	if effect_pools.has(effect_name):
		var pool = effect_pools[effect_name]
		var index = pool.in_use.find(effect)
		if index != -1:
			pool.in_use.remove_at(index)
			pool.available.append(effect)
			effect.visible = false
			if effect.get_parent():
				effect.get_parent().remove_child(effect)
			return
	
	# Otherwise destroy
	effect.queue_free()

func _remove_oldest_effect() -> void:
	if active_effects.size() > 0:
		var oldest_effect = active_effects[0]
		_cleanup_effect(oldest_effect, "")
		active_effects.remove_at(0)

## Event Handlers
func _on_damage_dealt(target: Node, damage: int, damage_type: String) -> void:
	if not target:
		return
	
	var effect_name = "hit_impact"
	
	# Choose effect based on damage type and amount
	if damage >= 50:  # High damage
		effect_name = "critical_hit"
	
	create_effect(effect_name, target.global_position)
	
	# Screen shake for big hits
	if damage >= 30:
		var shake_intensity = min(damage * 0.1, 5.0)
		_on_screen_shake_requested(shake_intensity, 0.3)

## Utility Functions
func set_effect_quality(quality: float) -> void:
	effect_quality = clamp(quality, 0.25, 1.5)

func get_active_effect_count() -> int:
	return active_effects.size()

func clear_all_effects() -> void:
	for effect in active_effects:
		if is_instance_valid(effect):
			effect.queue_free()
	active_effects.clear()

## Debug Functions
func debug_test_all_effects() -> void:
	if not main_scene:
		return
	
	var test_position = Vector3.ZERO
	var offset = 0
	
	for effect_name in effect_templates:
		create_effect(effect_name, test_position + Vector3(offset * 2, 0, 0))
		offset += 1

func debug_print_effect_state() -> void:
	print("=== Effect Manager Debug ===")
	print("Active Effects: %d/%d" % [active_effects.size(), max_active_effects])
	print("Effect Quality: %.2f" % effect_quality)
	print("Screen Shake: %.2f (%.2fs remaining)" % [screen_shake_intensity, screen_shake_timer])
	
	for pool_name in effect_pools:
		var pool = effect_pools[pool_name]
		print("Pool %s: %d available, %d in use" % [pool_name, pool.available.size(), pool.in_use.size()])
	print("============================")
